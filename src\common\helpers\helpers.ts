import { HttpException } from "@nestjs/common";

export const extractPaginationData = (query: QueryOptions): QueryOptions => {
  setParam('offset', query);
  setParam('limit', query);

  return query
}

export const extractSortingData = (query: QueryOptions): QueryOptions => {
  setParam('sort', query);
  setParam('way', query);

  return query
}

function setParam(param: string, query: QueryOptions) {
  if (query?.filter && `${param}` in query.filter) {
    query[param] = query?.filter[param];
    delete query?.filter[param];
  }
}

export const setResponse = (status: number, message: string, data?: any): QueryResult => {
  return (data) ? { status, message, data } : { status, message };
}

export const setResponseController = (data?: any): QueryResult => {
  if (data?.error || data instanceof Error) {
    throw new HttpException(data?.message || data?.name, data?.statusCode || data?.status || 500);
  }
  return data;
}

export const convertParams = (query: QueryOptions): QueryOptions => {
  if (query.filter) {
    for (const key in query.filter) {
      if (query.filter[key] === 'true') { query.filter[key] = true; }
      if (query.filter[key] === 'false') { query.filter[key] = false; }
      if (RegExp(/[a-z]/i).test(query.filter[key])) { continue; }
      query.filter[key] = !isNaN(query.filter[key]) ? +query.filter[key] : query.filter[key];
    }
  }

  if (query.limit) { query.limit = +query.limit; }
  if (query.offset) { query.limit = +query.offset; }

  return query;
}

export const ensureError = (value: unknown): Error => {
  if (value instanceof Error) return value;

  let stringified = '[Unable to stringify the thrown value]';
  try {
    stringified = JSON.stringify(value);
  } catch { }

  const error = new Error(
    `This value was thrown as is, not through an Error: ${stringified}`,
  );
  return error;
};

export const getErrorMessage = (error: Error): string => {
  const matchResult = error.message.match(/{.*}/);
  let errorData: { message?: string; data?: ObjectType<any> } = {};
  if (matchResult) errorData = JSON.parse(matchResult[0]);

  const messagesMap = {
    '60019':
      'Votre solde Orange Money est insuffisant pour procéder au paiement de votre commande',
  };

  return (
    messagesMap[errorData.data?.inittxnstatus] ??
    'Une erreur est survenue lors du paiement de votre commande, veuillez refaire votre commande.'
  );
};

export const getRandomString = (size): string => {
  size = size || 10;
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZ';
  let randomstring = '';
  for (let i = 0; i < size; i++) {
    const rnum = Math.floor(Math.random() * chars.length);
    randomstring += chars.substring(rnum, rnum + 1);
  }
  return randomstring;
}