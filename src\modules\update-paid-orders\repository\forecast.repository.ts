import { BaseRepository } from "@mycimencam-cron/common";

import moment from "moment";

export class ForecastRepository extends BaseRepository {

 
  constructor() {
    super();
  }

  
  async getForecast(order) {
    const productIds = order.cart.items.map(item => item.product._id.toString()) || [];
    const dateRange = {
      $gte: moment().startOf('day').valueOf(), 
      $lte: moment().endOf('year').valueOf(),  
    };
    
    return (await this.getCollection()).aggregate([
      {
        '$match': {
          'store._id': `${order?.cart?.store?._id}`,
          'created_at':dateRange,
          'items.product._id': { '$in': productIds },
          'packaging._id': `${order?.cart?.packaging?._id}`,
          'enable': true
        }
      }
    ]).toArray();
  }


}