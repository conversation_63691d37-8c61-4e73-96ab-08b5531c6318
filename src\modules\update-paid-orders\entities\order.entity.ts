import { Payment, PaymentMode } from "@mycimencam-cron/types";
import { Company } from "./company.entity";
import { Cart } from "./compute_price.entity";
import { Removal } from "./removal.entity";
import { BaseUser } from "./user.entity";

export class Order {
  _id?: string;
  appReference: string;
  erpReference: string;
  payment: Payment;
  cart: Cart;
  status: OrderStatus;
  customerReference?: string;
  user: Partial<BaseUser>;
  company?: Partial<Company>;
  dates?: {
    created: number;
    paid?: number;
    validated?: number;
  };
  removals: Removal[];
  validation: OrderValidation;
  created_at: number;
  rejectReason: string;
  snitch: {
    user: string,
    date: number,
    action: string,
  }
  nbrTries: number;
  points?:number;
  retryCountOrder?: number;
  generatedPoints?: number;

}

export enum OrderStatus {
  CREATED = 100,
  PAID = 200,
  PENDING = 202,
  VALIDATED = 300,
  FAILD = 400,
  CREDIT_REJECTED = 99,
  CREDIT_IN_VALIDATION = 101,

  CREDIT_IN_AWAIT_VALIDATION = 102,
  CANCELLED = 500,
  RETREIVED = 600,
}

export enum OrderValidation {
  CORDO_RH = 1,
  DRH = 2
}

export type PaymentPayload = {
  _id: string;
  order: {
    _id: string;
    appReference: string;
  };
  paymentInfo: Payment;
  amount: number;
  nbrTries: number;
};

export const PaymentModeLabel = {
  [PaymentMode.MY_ACCOUNT]: 'MY_ACCOUNT',
  [PaymentMode.AFRILAND]: 'AFRILAND',
  [PaymentMode.ORANGE_MONEY]: 'OM',
  [PaymentMode.MOBILE_MONEY]: 'MOMO',
  [PaymentMode.EXPRESS_EXCHANGE]: 'EXPRESS_EXCHANGE',
  [PaymentMode.CREDIT]: 'IN_CREDIT',
};