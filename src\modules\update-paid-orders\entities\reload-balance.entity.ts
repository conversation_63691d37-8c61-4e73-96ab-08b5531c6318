import { PaymentMode } from "@mycimencam-cron/types";
import { Company } from "./company.entity";
import { User } from "./user.entity";

export class ReloadBalance {
    _id: string;
    user: Partial<User>;
    nbrTries: number;
    company: Partial<Company>;
    status: TransactionStatus;
    currentBalance?: Balance;
    payment: PaymentReloadBalance;
    uniqueReference?: string;
    paymentInfo?: PaymentInfo<'status'>;
    created_at?: number;
}

export interface PaymentReloadBalancePayload {
    _id: string;
    payment: PaymentReloadBalance;
}

export enum TransactionStatus {
    PENDING = 200,
    SUCCESSFUL = 300,
    INITIATED = 100,
    FAILED = 99,
    EXPIRED = 400,
}

export class PaymentReloadBalance {
    paymentMode?: {
        id: PaymentMode;
        label: string;
        icon: string;
        txt: string;
    };
    reference: string;
    amount: number;
    bank: string;
    tel?: string;
    transactionId?: string;
    jdeTransactionId?: string;
}

export class Balance {
    amount: number;
    date: Date;
}

export enum ErrorCodeStatus {
    SUCCESSFUL = "0",
    NOT_AUTHENTICATE = "1",
    ERROR_OCCUR = "2",
    PARAM_ERROR = "3",
    PAYMENT_EXIST = "4"
}