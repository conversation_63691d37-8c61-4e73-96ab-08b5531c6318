import { Address } from "cluster";
import { Company } from "./company.entity";
import { Store } from "./store.entity";

export class BaseUser {
  _id?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  tel?: number;
  address?: Address;
  category?: UserCategory;
  roles?: string[];
  authorizations?: string[];
  profiles?: any;
  password?: string;
  create_at?: number;
  updateAt?: number;
  afrilandKey?: string;
  enable?: boolean;
  cni?: string;
  nui?: string;

}

export class AuthUser extends BaseUser {
  accessToken: string;
}

export class EmployeeCimencam extends BaseUser {
  isValidated?: boolean;
  isRetired?: boolean;
  direction?: string;
  service?: string;
  position?: string;
  employeeType: EmployeeType;
  matricule?: string;
  tonnage: Tonnage;
  store: Partial<Store>;
}

export class Retailer extends BaseUser {
  socialReason?: string;
  points?: number;
}

export class Particular extends BaseUser {
  profession?: string;
  points?: number;
}

export class CompanyEmployee extends BaseUser {
  company?: Company;
}

export enum UserCategory {
  Particular = 0,
  Retailer = 1,
  EmployeeCimencam = 2,
  CompanyUser = 3,

  Commercial = 200,
  Administrator = 300,
}

export const UserCategoryScripts = {
  [UserCategory.Particular]: 'particular',
  [UserCategory.Retailer]: 'retailer',
  [UserCategory.EmployeeCimencam]: 'employeecimencam',
  [UserCategory.CompanyUser]: 'companyuser',

  [UserCategory.Commercial]: 'commercial',
  [UserCategory.Administrator]: 'administrator',
};

export enum EmployeeType {
  NORMAL = 100,
  CORDO_RH = 101,
  DRH = 102,
}

export interface Tonnage {
  capacity: number;
  capacityPerYear: number;
  // capacityLeft: number;
}

export type User =
  | BaseUser
  | CompanyEmployee
  | EmployeeCimencam
  | Retailer
  | Particular;
