import { <PERSON><PERSON><PERSON>ogger } from 'nestjs-pino';
import EventEmitter2 from 'eventemitter2';
import { Inject, Injectable } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { OrderRepository, PaymentRepository } from '../update-paid-orders/repository';
import { UserRepository } from '../update-paid-orders/repository/user.repository copy';
import { Cron, CronExpression } from '@nestjs/schedule';
import { EUTransactionStatus, Payment, PaymentMode, keyEventJDE } from '@mycimencam-cron/types';
import { lastValueFrom } from 'rxjs';
import { OnEvent } from '@nestjs/event-emitter';
import { Order, OrderStatus } from '../update-paid-orders/entities';
import { EmployeeCimencam, UserCategory } from '../update-paid-orders/entities/user.entity';
import { StoreDatabaseType } from '@mycimencam-cron/common/services/common.entity';
import { CommonService } from '@mycimencam-cron/common';
import { config } from 'convict-config';


@Injectable()
export class EuCronService {
    isRunning: boolean;
    running: boolean;
    constructor(
        private readonly logger: PinoLogger,
        private userRepository: UserRepository,
        private orderRepository: OrderRepository,
        private commonSrv: CommonService,
        private paymentRepo: PaymentRepository,
        private eventEmitter: EventEmitter2,
        @Inject('JDE') private readonly JDEService: ClientProxy,
        @Inject('QUEUE') private readonly queueClient: ClientProxy,
        @Inject('PAYMENT') private readonly paymentClient: ClientProxy,

    ) {
    }

    @Cron(CronExpression.EVERY_MINUTE, { disabled: config.get('activeCronCheckEUPayment') })
    async verificationEUPaymentMCN() {
        this.logger.info(`=|=|=| Start Cron verificationEUPaymentMCN for${config.get('systemName')} |=|=|=
            \n variable: ACTIVE_CRON_CHECK_EU_PAYMENT_MCM`);

        if (this.isRunning)
            return this.logger.warn(`Cron verificationEUPaymentMCN is already have process , skip this execution`);

        try {
            this.isRunning = true;
            await this.verificationEUPaymentCommon();
        } finally {
            this.isRunning = false;
        }
    }

    async verificationEUPaymentCommon() {
        const query = {
            status: { $ne: EUTransactionStatus.SUCCESSFUL },
            "paymentInfo.mode.id": PaymentMode.EU,
            $or: [{ nbrTries: { $lte: 10 } }, { nbrTries: { $exists: 0 } }],
            transactionId: { $exists: true }
        };

        try {
            const paymentsEU = await this.paymentRepo.findAll({ filter: query }) as unknown as Payment[];

            this.logger.info(`Start verificationEUPayment with  ${paymentsEU?.length} payments`);

            for (const payment of paymentsEU) {
                this.isRunning = true;

                const nbrTries = payment?.nbrTries ? (payment?.nbrTries + 1) : 1;
                await this.paymentRepo.update({ _id: payment?._id }, { "nbrTries": nbrTries });

                this.logger.info(`Call EU API to get payment status for this order ${payment?.order?.appReference} with transactionID: ${payment?.transactionId}`);

                const res = await lastValueFrom(this.paymentClient.send({ cmd: `${PaymentMode.EU}_payment_verification` },
                    this.commonSrv.EventData(payment?.transactionId)));

                if ('order' in payment && 'status' in res)
                    await this.eventEmitter.emitAsync('update_order_eu', res, payment?.order._id, payment?.transactionId);
            }
            this.isRunning = false;

        } catch (error) {
            this.logger.error(`error while running cron of verificationEUPayment ${JSON.stringify(error)}`);
            this.isRunning = false;
            return error;
        }

    }

    @OnEvent('update_order_eu', { async: true })
    async verifyEUPaymentStatus(db: StoreDatabaseType, response: { status: EUTransactionStatus; message: any; }, orderId: string, transactionId: string) {
        try {
            const order = await this.orderRepository.findOne({ filter: { _id: orderId } }) as unknown as Order;

            if (response?.status === EUTransactionStatus.FAILED && response?.message) {
                this.logger.error(`EU payment verification response for order ${order?._id} is failed. \n${JSON.stringify(response)}\n`);
                return await this.orderRepository.update({ _id: order._id }, { status: OrderStatus.FAILD });
            }

            if (!response?.status && !response?.message) return {
                message: `order id ${order?._id} payment process error`,
                status: 500
            }

            if (response.status === EUTransactionStatus.SUCCESSFUL) {
                await this.orderRepository.update({ _id: order?._id }, {
                    status: OrderStatus.PAID,
                    'payment.transactionID': transactionId,
                    'dates.paid': (new Date()).getTime()
                });

                const user = await this.userRepository.findOne({ filter: { _id: order?.user?._id } }) as unknown as EmployeeCimencam;

                if (user.category === UserCategory.Particular) {
                    const pointValidated = user['points'].validated + this.computeUserPoint(order);
                    await this.userRepository.update({ _id: user._id }, { 'points.validated': pointValidated });

                }

                if (user?.category === UserCategory.EmployeeCimencam && 'tonnage' in user) {
                    const capacityTonnage = user.tonnage.capacity - this.computeOrderTonnage(order);
                    const capacityTonnageYear = user?.tonnage?.capacityPerYear - this.computeOrderTonnage(order);
                    await this.userRepository.update({ _id: user._id }, { capacityTonnage, capacityTonnageYear });
                    this.logger.info(`Tonnage of ${user?.email} has been updated to capacityTonnage: ${capacityTonnage} and capacityTonnageYear ${capacityTonnageYear}.`);
                }

                this.JDEService.emit({ cmd: keyEventJDE.CREATE_ORDER }, { appReference: order.appReference });

                this.queueClient.emit('payment_received', order);
                this.queueClient.emit('commercial_validation', order);

                this.logger.info(`email notification sent to client and cimencam`);

                return {
                    message: `order id ${order._id} payment success`,
                    status: 200
                }

            }
        } catch (error) {
            this.queueClient.emit('send_error', {
                message: error,
                microService: 'CRON',
                methodPath: 'verifyEUPaymentStatus'
            });
            this.logger.error(`Error during procesing of response from EU ${error}`)
        }
    }

    private computeUserPoint(order: Order) {
        let points = 0;
        order.cart.items.forEach(item => {
            if (item?.product?.label === 'ROBUST') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 2; }
            if (item?.product?.label === 'MULTIX') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 2; }
            if (item?.product?.label === 'SUBLIM') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 3; }
            if (item?.product?.label === 'HYDRO 50kg') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 3; }
            if (item?.product?.label === 'HYDRO 25kg') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 3; }
        });
        return points;
    }

    private computeOrderTonnage(order: Order): number {
        let tonnage = 0;
        order.cart.items.forEach(item => {
            if (item.quantity) { tonnage += (item?.quantity / order?.cart?.packaging?.unit?.ratioToTone); }
        });
        return tonnage;
    }

}
