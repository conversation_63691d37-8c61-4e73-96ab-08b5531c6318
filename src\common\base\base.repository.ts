import { DatabaseMongoDB } from "@mycimencam-cron/database";
import { Injectable } from "@nestjs/common";
import { Collection, Document, InsertManyResult, InsertOneResult, ObjectId, UpdateResult, WithId } from "mongodb";
import { RepositoryInterface } from "../interfaces";
import { config } from "convict-config";
import moment from "moment";

@Injectable()
export class BaseRepository implements RepositoryInterface {

    protected collectionName: string;
    public database: DatabaseMongoDB;
    // public cimfig_database: MongoDatabaseCimfig;
    private snitchCollectionName = 'snitchs';

    constructor() {
        if (this.collectionName == null) this.collectionName = this.getCollectionName();
        this.database = DatabaseMongoDB.getInstance();
        // this.cimfig_database = MongoDatabaseCimfig.getInstance();
    }

  

    async createMany(documents: Document[]): Promise<InsertManyResult<Document>> {
        const res = await (await this.getCollection()).insertMany(documents);

        if (config.get('activeSnitch')) {

            const snitchObject: Snitch = {
                action: 'CREATE',
                user: 'SYSTEM',
                newData: {
                    _id: res?.insertedIds
                },
                collectionName: this.collectionName,
                description: `The SYSTEM created the data with id ${res?.insertedIds} in the collection ${this.collectionName} `,
                created_at: moment().valueOf()
            };
            (await this.getSnitchCollection()).insertOne(snitchObject);
        }

        return res;
    }

    async create(document: Document): Promise<InsertOneResult<Document>> {
        document['created_at'] = document?.created_at ?? moment().valueOf();

        const res = await (await this.getCollection()).insertOne(document);

        if (config.get('activeSnitch')) {

            const snitchObject: Snitch = {
                action: 'CREATE',
                user: 'SYSTEM',
                newData: {
                    _id: res?.insertedId
                },
                collectionName: this.collectionName,
                description: `The SYSTEM created the data with id ${res?.insertedId} in the collection ${this.collectionName} `,
                created_at: moment().valueOf()
            };
            (await this.getSnitchCollection()).insertOne(snitchObject);
        }

        return res;
    }

    async findAll(query: QueryOptions): Promise<Document[]> {
        return await (await this.getCollection()).find(query?.filter ?? {})
            .project(query?.projection ?? {})
            .sort(query?.sort ?? '_id', query?.way ?? -1)
            .skip(query?.offset ?? 0)
            .limit(query?.limit ?? 0)
            .toArray();
    }

    async findOne(query: QueryOptions): Promise<WithId<Document>> {
        this.setMongoId(query.filter);

        return await (await this.getCollection())
            .findOne(query.filter, { projection: query.projection ?? {} });
    }

    async count(query: QueryFilter): Promise<number> {
        return await (await this.getCollection()).countDocuments(query)
    }

    async update(filter: QueryFilter, document: Document): Promise<UpdateResult> {
        this.setMongoId(filter);

        const existVerify = await (await this.getCollection()).findOne({ _id: filter?._id })
        const res = await (await this.getCollection()).updateOne(filter, { $set: document })

        if (config.get('activeSnitch')) {
            const snitchObject: Snitch = {
                action: 'UPDATE',
                user: 'SYSTEM',
                newData: {
                    ...document
                },
                oldData: existVerify,
                collectionName: this.collectionName,
                description: `The SYSTEM carried out a modification on the data ${existVerify?._id} from the collection ${this?.collectionName}`,
                created_at: moment().valueOf()
            };
            (await this.getSnitchCollection()).insertOne(snitchObject);
        }

        return res;
    }

    protected async getCollection(): Promise<Collection<Document>> {
        return (await this.database.getDatabase()).collection(this.collectionName);
    }

    async getSnitchCollection(): Promise<Collection<Document>> {
        return (await this.database.getDatabase()).collection(this.snitchCollectionName);
    }

    private getCollectionName(): string {
        const className = this.constructor.name;
        const regexResult = className.match(/[A-Z][a-z]+/g);
        if (!regexResult) return '';

        let collectionName = '';
        const match = regexResult[1];

        collectionName = match === 'Repository'
            ? className.replace(match, '').toLowerCase()
            : className.replace(`${match}Repository`, `_${match}`).toLowerCase();

        return collectionName.endsWith('y')
            ? collectionName.replace('y', 'ies')
            : collectionName + 's';
    }

    private setMongoId(filter: QueryFilter): void {
        if (filter.hasOwnProperty('_id')) { filter._id = new ObjectId(filter._id) }
    }
}
