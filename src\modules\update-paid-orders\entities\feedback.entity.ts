import { Company } from "./company.entity";
import { Product } from "./product.entity";
import { User } from "./user.entity";

interface BaseFeedbackCategory {
  date: string;
  location: string;
  description?: Description;
}

export class SalesFeedback {
  type: DataType;
  data: FeedbackData;
  attachment?: Attachment;
  created_at?: number;
  commercial?: User;  
  status: string
}


export type FeedbackData = Prospect | Command | Stock | NewProduct | Quality;

// Prospection
export class Prospect implements BaseFeedbackCategory {
  objectVisit: string;
  clientType: string;
  interlocutor: string;  // Nom du contact
  pdv: string;  // Point de vente
  coordinates: string;  // Coordonnées GPS
  socialReason: Company;  // Raison sociale
  location: string;
  date: string;
  market: string;  // Type de marché
  stocks: string;  // État des stocks observés
  potentialCA?: number;  // Chiffre d'affaires potentiel
  nextActions?: string;  // Actions à prévoir
  competitorPresence?: string[];  // Présence concurrence
  description?: Description;
}

// Commande
export class Command {
  objectVisit: string;
  location: string;
  clientId: string;
  products: Product;
  totalAmount: number;
  paymentTerms: string;
  deliveryDate: string;
  specialRequirements?: string;
  description?: Description;
  frequentIssues?: string[];  
  specificNeeds?: string[];  
  budgetRange?: string; 
  supplierDependency?: 'multiple' | 'single';
  futureOrderGoals?: string[];  
}


// Qualité
export class Quality {
  objectVisit: string;
  date: string;
  location: string;
  productId: string;
  issueType: 'packaging' | 'product_quality' | 'expiration' | 'other';
  severity: 'low' | 'medium' | 'high' | 'critical';
  affectedBatch?: string;
  clientFeedback?: string;
  proposedSolution?: string;
  requiresImmediate: boolean;
  description?: Description;
}

export class Stock {
  objectVisit: string;
  date: string;
  location: string;
  productId: string;
  availableQuantity: number;
  stockPosition: 'excess' | 'normal' | 'low' | 'critical';
  stockExpireDate: string;
  stockRotation: 'fast' | 'medium' | 'slow';
  quantityOrderedAvg: string;
  reorderPoint?: number;
  shelfVisibility: 'excellent' | 'good' | 'poor';
  competitorStock?: {
    brand: string;
    quantity: number;
  }[];
  description?: Description;
}

export class NewProduct {
  objectVisit: string;
  clientType: string;
  coordinates: string;
  socialReason: string;
  location: string;
  date: string;
  market: string;
  stocks: number;
  description?: string;
}
// Interfaces communes
export interface Description {
  title: string;
  descriptive: string;
  tags?: string[];
  priority?: 'low' | 'medium' | 'high';
}

export interface Attachment {
  file: string;
  name: string;
  contentType: string;
  size?: number;
  uploadDate?: string;
}

export enum DataType{
  Prospect = 'prospect',
  Command = 'command',
  Stock = 'stock',
  NewProduct = 'newProduct',
  Quality = 'quality',
}
