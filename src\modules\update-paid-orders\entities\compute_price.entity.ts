import { Packaging } from "./packaging.entity";
import { Product } from "./product.entity";
import { Shipping } from "./shipping.entity";
import { Store } from "./store.entity";

export class Cart {
    store: Partial<Store>;
    packaging: Partial<Packaging>;
    items: CartItem[];
    renderType: RenderType;
    shipping?: Shipping;
    amount: OrderPrice;
}

export declare type CartItem = {
    product: Partial<Product>;
    quantity: number;
    unitPrice: number;
}

export declare type OrderPrice = {
    HT: number,
    subTotal?: number, //TODO: add value in this feild
    precompte?: number,
    VAT: number,
    shipping: number,
    TTC: number
}

export enum RenderType {
    PICKUP = 1,
    RENDU = 2
}

export const RenderTypeValueLibrary = {
    [RenderType.PICKUP]: "C9",
    [RenderType.RENDU]: "A0",
}
