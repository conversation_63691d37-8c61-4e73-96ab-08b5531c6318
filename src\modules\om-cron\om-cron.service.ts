import { UserRepository } from './../update-paid-orders/repository/user.repository copy';
import { lastValueFrom } from 'rxjs';
import { config } from 'convict-config';
import { PinoLogger } from 'nestjs-pino';
import { ClientProxy } from '@nestjs/microservices';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Order, OrderStatus } from '../update-paid-orders/entities';
import { Inject, Injectable } from '@nestjs/common';
import { CompanyRepository, OrderRepository, PaymentRepository, ReloadBalanceRepository } from '../update-paid-orders/repository';
import { OperatorTransactionStatus, Payment, PaymentMode } from '@mycimencam-cron/types';
import { EmployeeCimencam, UserCategory } from '../update-paid-orders/entities/user.entity';
import moment from 'moment';
import { CommonService } from '@mycimencam-cron/common';


@Injectable()
export class OmCronService {

    credential = Buffer.from(
        `${config.get('payment.orange.api_username')}:${config.get(
            'payment.orange.api_password',
        )}`,
    ).toString('base64');

    transactionId: string;
    user: any;
    isProcessingMCM: boolean;
    isProcessingCimfig: boolean;


    constructor(
        private orderRepo: OrderRepository,
        private readonly logger: PinoLogger,
        private eventEmitter: EventEmitter2,
        private paymentRepo: PaymentRepository,
        private userRepo: UserRepository,
        private commonSrv: CommonService,
        private companyRepo: CompanyRepository,
        private reloadBalanceRepository: ReloadBalanceRepository,
        @Inject('QUEUE') private readonly queueClient: ClientProxy,
        @Inject('PAYMENT') private readonly paymentClient: ClientProxy,
    ) {
    }

    @Cron(CronExpression.EVERY_MINUTE, { disabled: config.get('activeCronCheckOrangePayment') })
    async transactionPaymentOrangeVerificationMCM() {

        if (this.isProcessingMCM)
            return this.logger.warn(`MCM transactionPaymentOrangeVerificationMCM cron is already running, skipping`);

        this.isProcessingMCM = true;
        this.logger.info(`=|=|=| Start Cron verificationOrangeVerificationMCM for ${config.get('systemName')} |=|=|=
            \n variable: ACTIVE_CRON_CHECK_ORANGE_PAYMENT\n`);

            try {
            await this.orangeVerificationPayment();
        } finally {
            this.isProcessingMCM = false;
            this.logger.info('MCM transactionPaymentOrangeVerificationMCM cron end');
        }
    }

    private async orangeVerificationPayment() {
        const pageSize = config.get('nbrTries');
        const concurrency = config.get('concurrency');
        const baseQuery = {
            "paymentInfo.mode.id": PaymentMode.ORANGE_MONEY,
            "paymentResponse.data.status": { $in: [OperatorTransactionStatus.PENDING, OperatorTransactionStatus.INITIATED] },
            $and: [{
                "paymentResponse.data.txnid": { $exists: true },
                'paymentResponse.data.payToken': { $exists: true },
                $or: [{ "nbrTries": { $exists: false } }, { "nbrTries": { $lte: config.get('nbrTries')  } }],
            }],
        }

        let page = 0;
        while (true) {
            const payments = (await this.paymentRepo.findAll({
                filter: baseQuery,
                offset: page * pageSize,
                limit: pageSize
            })) as unknown as Payment[];

            if (!payments?.length) break;
            
            this.logger.info(`=|=|=| Start Cron transactionPaymentOrangeVerification for ${config.get('systemName')} |=|=|=
            \n variable: ACTIVE_CRON_CHECK_ORANGE_PAYMENT_MCM\n`);
            this.logger.info(`Processing page ${page + 1}, ${payments?.length} Orange payments`);
            await this.runWithConcurrency(payments, concurrency);
            page++;
        }
    }

    private async runWithConcurrency(payments: Payment[], concurrency: number,) {
        let idx = 0;
        const worker = async () => {
            while (idx < payments?.length) {
                const payment = payments[idx++];
                try {
                    await this.paymentOrangeVerification(payment);
                } catch (err) {
                    this.logger.error(`Error on payment ${payment?._id}: ${err}`);
                }
            }
        };
        await Promise.all(Array(concurrency).fill(null).map(() => worker()));
    }

    private async paymentOrangeVerification(payment: QueryFilter) {
        // this.isRunning = true;
        const nbrTries = payment['nbrTries'] ? (+payment['nbrTries'] + 1) : 1;
        await this.paymentRepo.update({ _id: payment._id }, { "nbrTries": nbrTries });

        this.logger.info(`ORANGE payment verification ${payment?.paymentResponse?.data?.payToken} for ${payment?.order?.appReference || payment?.reloadBalance?._id}`);

        const { status } = await lastValueFrom(this.paymentClient.send({ cmd: `${PaymentMode.ORANGE_MONEY}_payment_verification` },
            this.commonSrv.EventData(payment?.paymentResponse?.data?.payToken)));

        this.logger.info(`ORANGE response for payment ${payment?.paymentResponse?.data?.payToken} verification: ${JSON.stringify(status)}`);
        const transactionId = payment?.paymentResponse?.data?.payToken;

        if ('reloadBalance' in payment && 
            [OperatorTransactionStatus.SUCCESSFUL, 'SUCCESSFULL', OperatorTransactionStatus.PENDING, OperatorTransactionStatus.FAILED].includes(status)) {
            await this.reloadBalanceRepository.update(
                { "paymentInfo.transactionId": transactionId }, {
                "paymentInfo.status": status
            });
        }

        if ('order' in payment && [OperatorTransactionStatus.SUCCESSFUL, 'SUCCESSFULL'].includes(status))
            await this.eventEmitter.emitAsync('update_order_om', transactionId, { ...payment?.order, payment: payment.paymentInfo })
    }

    @OnEvent('update_order_om', { async: true })
    async UpdateOrder(transactionId: string, order: Order) {
        this.logger.info(`Receive event to update order: ${order?.appReference} in update_order_om event.`);

        const { payment: { mode: { id } } } = order;
        await this.commonSrv.VerifyPointEligibility(id, order, this.companyRepo);

        await this.orderRepo.update({ _id: order?._id }, {
            status: OrderStatus.PAID,
            'payment.transactionID': transactionId,
            'dates.paid': moment().valueOf()
        });

        this.logger.info(`Status de la commande ${order.appReference} mis à jour à payée.`);
        this.queueClient.emit('payment_successful', this.commonSrv.EventData(order));

        const user = await this.userRepo.findOne({ filter: { _id: order?.user?._id } }) as unknown as EmployeeCimencam;

        if (user.category === UserCategory.Particular) {
            const pointValidated = user['points'].validated + this.computeUserPoint(order);
            await this.userRepo.update({ _id: user._id }, { 'points.validated': pointValidated });

            await this.orderRepo.update({ _id: order?._id }, { points: this.computeUserPoint(order) });
        }

        if (user?.category === UserCategory.EmployeeCimencam && 'tonnage' in user) {
            const capacityTonnage = user.tonnage.capacity - this.computeOrderTonnage(order);
            const capacityTonnageYear = user?.tonnage?.capacityPerYear - this.computeOrderTonnage(order);
            await this.userRepo.update({ _id: user._id }, { capacityTonnage, capacityTonnageYear });
            this.logger.info(`Tonnage of ${user?.email} has been updated to capacityTonnage: ${capacityTonnage} and capacityTonnageYear ${capacityTonnageYear}.`);

        }

        this.queueClient.emit('commercial_validation', this.commonSrv.EventData(order));

        this.logger.info(`email notification sent to client and cimencam`);
    }

    private computeUserPoint(order: Order) {
        let points = 0;
        order?.cart?.items.forEach(item => {
            if (item?.product?.label === 'ROBUST') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 2; }
            if (item?.product?.label === 'MULTIX') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 2; }
            if (item?.product?.label === 'SUBLIM') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 3; }
            if (item?.product?.label === 'HYDRO 50kg') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 3; }
            if (item?.product?.label === 'HYDRO 25kg') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 3; }
        });
        return points;
    }

    private computeOrderTonnage(order: Order): number {
        let tonnage = 0;
        order?.cart?.items.forEach(item => {
            if (item.quantity) { tonnage += (item?.quantity / order?.cart?.packaging?.unit?.ratioToTone); }
        });
        return tonnage;
    }
}


