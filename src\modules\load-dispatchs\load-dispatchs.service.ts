import { Inject, Injectable } from '@nestjs/common';
import { config } from 'convict-config';
import http from 'request-promise';
import moment from 'moment-timezone'; import { AEstatus } from '@mycimencam-cron/types';
import { Authorization_RemovalRepository } from './repository';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ClientProxy } from '@nestjs/microservices';
import { PinoLogger } from 'nestjs-pino';
import { OrderRepository } from '../update-paid-orders/repository';
import { Order, OrderStatus } from '../update-paid-orders/entities';
import { StatusAES } from './entities';
import { AutomaticValidatedOrderEvent } from './events/order-validatedBy-cimen.event .ts';
import { Company } from '../update-paid-orders/entities/company.entity';
import { CompanyRepository } from '../update-paid-orders/repository/company.repository';
import { UserRepository } from '../update-paid-orders/repository/user.repository copy';
import { EmployeeCimencam } from '../update-paid-orders/entities/user.entity';
import { CommonService } from '@mycimencam-cron/common';
import { SchedulesRepository } from '../update-paid-orders/repository/schedule.repository';
import { StoreDatabaseType } from '@mycimencam-cron/common/services/common.entity';

@Injectable()
export class LoadDispatchsService {
    cronProcessingCimfig: boolean;
    cronProcessing = false;
    loadDispatches: any;
    replacementResults: any[] = [];

    constructor(
        private commonSrv: CommonService,
        private readonly logger: PinoLogger,
        private userRepository: UserRepository,
        private orderRepository: OrderRepository,
        private companyRepository: CompanyRepository,
        private scheduleRepository: SchedulesRepository,
        private AERepository: Authorization_RemovalRepository,
        @Inject('QUEUE') private readonly queueClient: ClientProxy,
    ) { }

    @Cron(CronExpression.EVERY_MINUTE, { disabled: config.get('requestForRetrievementJDE') })
    async getAEs() {
        this.logger.info(`=|=|=| Start Cron getAEs for ${config.get('systemName')} |=|=|=
            \n variable: ACTIVE_CRON_REQUEST_AES_JDE_MCM\n`);

        if (this.cronProcessing) return this.logger.info(`skip Processing fetchLoadDispatches cause the operation is in process`);

        this.cronProcessing = true;
        const systemName = config.get('systemName');
        const businessUnitCondition = systemName === StoreDatabaseType.CIMFIG ? `{$in:['CM20010']}` : `{$ne:'CM20010'}`;
        await this.getAESCommon(`fetchedBy.${config.get('LoadDispatchQuerySystemName')}=false&&LoadDispatchDetails.Details.BusinessUnit=${businessUnitCondition}`);
        this.cronProcessing = false;

    }

    async getAESCommon(query: string) {
        try {
            this.loadDispatches = await this.fetchLoadDispatches(query);

            this.logger.info(`loadDispatches length: ${this.loadDispatches?.length}`);
            if (this.loadDispatches?.length) {
                this.replacementResults = await Promise.all(
                    this.loadDispatches.map(loadDispatch => this.processLoadDispatch(loadDispatch)));

                await this.updateFetchedLoadDispatches(
                    this.replacementResults?.filter((result) => result != undefined || result != null));
            }
        } catch (error) {
            this.logger.error(`order update failed, message: ${error?.message} : \n ${error?.stack}`);
            return error;
        } finally { this.cronProcessing = false }
    }

    async fetchLoadDispatches(query: string) {
        try {
            this.logger.info(`start Processing fetchedLoadDispatches on ${moment().tz("Africa/Douala").format("YYYY/MM/DD HH:mm:ss")}`);
            const options = {
                method: 'GET',
                uri: config.get('loadDispatch.apiUrl') + `/load-dispatchs?${query}`,
                auth: {
                    user: config.get('loadDispatch.login'),
                    pass: config.get('loadDispatch.password')
                },
                json: true
            }

            return await http(options);
        } catch (error) {
            this.queueClient.emit('send_error', this.commonSrv.EventData({
                message: error,
                microService: 'CRON',
                methodPath: 'fetchLoadDispatchs'
            }));
            this.logger.error(`[environment: ${config.get('env')}] Getting loadDispatchs failed \n${error.message} \n ${error.stack}`);
        }
    }

    async processLoadDispatch(loadDispatch) {
        const authorizationRemoval = this.formatLoadDispatch(loadDispatch);
        authorizationRemoval.enable = true;
        const result = await this.AERepository.replaceAuthRemoval(authorizationRemoval);

        if (authorizationRemoval.LoadStatus && [StatusAES.CREATED_APPROVED, StatusAES.CHECK_IN, StatusAES.COMPLETE, StatusAES.TRANSIT].includes(authorizationRemoval.LoadStatus)) {
            await this.validateOrder(authorizationRemoval);
        }

        if (result.upsertedId) {
            const query = this.formatQuery(authorizationRemoval);
            const data = await this.scheduleRepository.findAll(query);
            if (data?.length) {
                authorizationRemoval['schedule'] = data[0];
                await this.scheduleRepository.update({ _id: data[0]['_id'] }, { 'LoadNumber': authorizationRemoval?.LoadNumber });
                this.logger.info(`Matched AE with LoadNumber : ${authorizationRemoval?.LoadNumber} to schedule with id: ${data[0]['_id']}`);

            }
            await this.processAuthRemoval(authorizationRemoval, result);
        }

        return { LoadNumber: authorizationRemoval.LoadNumber };
    }

    async validateOrder(authorizationRemoval) {
        try {

            const query = { erpReference: authorizationRemoval.SalesOrderNumber };

            const order: Order = await this.orderRepository.findOne({ filter: query }) as unknown as Order;

            if (order && order.status === OrderStatus.PAID) {
                this.logger.info(`Validating order with erpReference: ${authorizationRemoval.SalesOrderNumber}`);

                order.dates.validated = (new Date()).getTime();
                const res = await this.orderRepository.update(query, { status: OrderStatus.VALIDATED, "dates.validated": (new Date()).getTime() });

                if (!res) {
                    this.logger.info(`Error during update of order status to 300`);
                    return new Error('OrderNotUpdated');
                }

                this.logger.info(` Send email for Validating order with erpReference: ${authorizationRemoval.SalesOrderNumber}`);
                this.queueClient.emit('automatic-validated-order', this.commonSrv.EventData(new AutomaticValidatedOrderEvent(order)));
            }
        } catch (error) {
            this.logger.error(`Fail to validate order with erpReference: ${authorizationRemoval.SalesOrderNumber}`);
            return new Error("Fail to validate order");
        }
    }

    async processAuthRemoval(authorizationRemoval, result) {
        const currentDate = moment().tz('Africa/Douala');
        if (result.upsertedId) {
            await this.AERepository.update(
                { _id: result.upsertedId.toString() },
                { status: AEstatus[authorizationRemoval?.FreightHandlingCode] ?? AEstatus.A0 });
        }
        this.replacementResults.push({ LoadNumber: authorizationRemoval.LoadNumber });

        this.logger.info(`sending fetched loadDispatchs for update on ${currentDate.format('YYYY/MM/DD HH:mm:ss')}`);
        await this.updateFetchedLoadDispatches(this.replacementResults.filter(result => result != undefined || result != null));

        if (authorizationRemoval.LoadStatus == StatusAES.TRANSIT) {
            const query = { erpSoldToId: Number(authorizationRemoval?.SoldTo) };
            const company = await this.companyRepository.findOne({ filter: query }) as unknown as Company;
            const user = await this.userRepository.findOne({ filter: { associatedCompanies: { $elemMatch: { "_id": company._id.toString() } } } }) as unknown as EmployeeCimencam;

            this.logger.info(`Sending message for the loading of removal ${authorizationRemoval.LoadNumber} to  ${company?.associatedCommercial?.tel || user?.tel || config.get("telServiceClient")} `);

            this.queueClient.emit('sms_received', this.commonSrv.EventData(
                {
                    receiver: ['development', 'staging'].includes(config.get("env"))
                        ? config.get("telServiceClient")
                        : company?.associatedCommercial?.tel || user?.tel || config.get("telServiceClient"),
                    message: `Bonjour Mme/M. L'authorisation d'enlèvement ${authorizationRemoval.LoadNumber} liée à la commande 
                    ${authorizationRemoval.Instruction1}, vient d'être chargée.\n Merci pour votre attention.`
                }));
        }
    }

    async updateFetchedLoadDispatches(fetchLoads: any[]) {
        const clientValue = config.get('LoadDispatchQuerySystemName');
        fetchLoads = fetchLoads.map(load => { return { ...load, client: clientValue } });

        try {
            const options = {
                method: 'POST',
                uri: config.get('loadDispatch.apiUrl') + '/load-dispatchs',
                auth: {
                    user: config.get('loadDispatch.login'),
                    pass: config.get('loadDispatch.password')
                },
                body: fetchLoads,
                json: true
            }

            return await http(options);
        } catch (error) {
            this.queueClient.emit('send_error', this.commonSrv.EventData({
                message: error,
                microService: 'CRON',
                methodPath: 'updateFetchedLoadDispatchs'
            }));
            this.logger.error(`[environment: ${config.get('env')}] Getting loadDispatchs failed \n${error.message} \n ${error.stack}`);
        }
    }

    formatLoadDispatch(loadDispatch) {
        const { LoadDispatchDetails: { Details }, ...rest } = loadDispatch;
        const detailContent = Array.isArray(Details) && Details.length > 0 ? { ...Details[0] } : { ...Details };

        return { ...rest, ...detailContent };
    }

    formatQuery(authorizationRemoval) {
        // Build query dynamically, only including non-empty fields
        const queryDate = moment(authorizationRemoval?.DateRequestedShipment, 'YYYY/MM/DD').format('YYYY-MM-DD');
        const rawQuery: Record<string, any> = {
            erpItemId: Number(authorizationRemoval?.ShortItemNumber),
            quantity: Number(authorizationRemoval?.TransactionQuantity),
            SalesOrderNumber: authorizationRemoval?.SalesOrderNumber,
            appReference: authorizationRemoval?.Instruction1?.replace(',', ''),
            date: { $regex: `^${queryDate}` },
            LoadNumber: { $exists: 0 },
            enable: true
        };

        // Remove keys with empty, undefined, or null values
        const query: Record<string, any> = {};
        for (const [key, value] of Object.entries(rawQuery)) {
            // For 'date' and 'enable', always include as they are always needed
            if (key === 'date' || key === 'enable') {
                query[key] = value;
            } else if (!['', 'undefined', 'null', undefined, null].includes(value) &&
                !(typeof value === 'number' && isNaN(value))
            ) {
                query[key] = value;
            }
        }
        return query;
    }
}
