import { ObjectId } from 'mongodb';
import { <PERSON><PERSON><PERSON>og<PERSON> } from 'nestjs-pino';
import { Injectable } from '@nestjs/common';
import { ClientStockRepository } from './repository';
import { CartItem, Order, Packaging } from '../update-paid-orders/entities';
import { ClientStock, StockMovementType } from './entities/client-stock.entity';

@Injectable()
export class ClientStockService{

  constructor(private readonly clientStockRepository: ClientStockRepository,
              private readonly logger: PinoLogger) { }

  async createClientStock(order: Order): Promise<any> {
    try {
      if (!order?.company?._id) {
        this.logger.error('Company ID is missing in the order object');
        throw new Error("Comoany ID is missing in the order object.");
      }
  
      // Construire la requête pour trouver les stocks existants
      const query: QueryOptions = {
        filter: {
          $or: [
            { 'company._id': order?.company?._id.toString() },
            { 'company._id': new ObjectId(order?.company?._id) },
          ],
        },
      };
  
      // Vérifier si un stock client existe déjà
      const existingClientStocks = (await this.clientStockRepository.findAll(query)) as ClientStock[];
      if (existingClientStocks?.length > 0) {
        return await this.processToUpdate(order, existingClientStocks[0]);
      }
  
      // Préparer les produits pour currentProductInStock
      const currentProductInStock = order.cart.items.map((item) => ({
        product: item?.product,
        quantity: item?.quantity,
        unitPrice: item?.unitPrice,
        packaging: order?.cart?.packaging,
      }));
  
      // Préparer les mouvements de stock
      const stockMovements = order?.cart?.items?.map((item) => ({
        type: StockMovementType.ENTRY,
        date: Date.now(),
        product: item?.product,
        quantity: item?.quantity,
        unitPrice: item?.unitPrice,
        packaging: order?.cart?.packaging,
      }));
      
      // Créer un nouvel objet ClientStock
      const newClientStock: ClientStock = {
        user: {
          _id: order?.user?._id,
          email: order?.user?.email,
        },
        company:{
          _id: order?.company?._id,
          name: order?.company?.name,
          rccm: order?.company?.rccm,
          erpSoldToId: order?.company?.erpSoldToId
        },
        currentProductInStock,
        stockMovements,
      };
  
      // Enregistrer le nouveau stock client
      return await this.clientStockRepository.create(newClientStock);
    } catch (error) {
      this.logger.error(  `Error while creating client stock for Order ID: ${order?._id}. Details: `, JSON.stringify(error?.message));
      throw error;
    }
  }
  
  private async processToUpdate(order: Order, existingStock: ClientStock): Promise<void> {
    // Consolider les produits dans le panier pour éviter les doublons
    const consolidatedItems = this.consolidateItems(order?.cart?.items, order?.cart?.packaging);
  
    for (const item of consolidatedItems) {
      await this.processStockEntry(item, order?.cart?.packaging, existingStock,);
    }
  
    // Vérification finale et mise à jour de la base
    
    await this.clientStockRepository.update({ _id: existingStock?._id }, existingStock);
  }
  
  // Consolidation des produits identiques
  private consolidateItems(items: CartItem[], packaging: Partial<Packaging>): CartItem[] {
    const consolidatedMap = new Map<string, CartItem>();
  
    for (const item of items) {
      const key = `${item?.product?._id}-${packaging?._id}`;
      if (consolidatedMap.has(key)) {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const existingItem = consolidatedMap.get(key)!;
        existingItem.quantity = (existingItem?.quantity || 0) + (item?.quantity || 0);
      } else {
        consolidatedMap.set(key, { ...item });
      }
    }
  
    return Array.from(consolidatedMap.values());
  }
  
  // Mise à jour ou ajout des produits dans le stock
 private async processStockEntry(item: CartItem, packaging: Partial<Packaging>, existingStock: ClientStock): Promise<void> {
    existingStock.currentProductInStock = existingStock?.currentProductInStock || [];
  
    const existingProductIndex = existingStock.currentProductInStock.findIndex(
      (productInStock) =>
        productInStock?.product?._id === item?.product?._id &&
        productInStock?.packaging?._id === packaging?._id
    );
  
    if (existingProductIndex !== -1) {
      // Si le produit existe, mettre à jour sa quantité
      existingStock.currentProductInStock[existingProductIndex].quantity =
        (existingStock.currentProductInStock[existingProductIndex].quantity || 0) + (item?.quantity || 0);
    } else {
      // Sinon, ajouter le produit au stock
      existingStock.currentProductInStock.push({
        product: item?.product,
        quantity: item?.quantity,
        unitPrice: item?.unitPrice,
        packaging: packaging,
      });
    }
  
    // Ajouter un mouvement de stock
    existingStock.stockMovements = existingStock.stockMovements || [];
    existingStock.stockMovements.push({
      type: StockMovementType.ENTRY,
      date: Date.now(),
      product: item?.product,
      quantity: item?.quantity,
      unitPrice: item?.unitPrice,
      packaging: packaging
    });
  }
  
}
