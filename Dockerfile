###################
# BUILD FOR PRODUCTION
###################

FROM node:16-alpine

ENV NODE_ENV build

COPY package*.json /tmp/

# Install dependecies
# RUN cd /tmp && npm ci && npm cache clean --force && npm i typescript
RUN cd /tmp && npm install && npm i -g @nestjs/cli

COPY nest-cli.json tsconfig*.json convict-config.ts /tmp/

COPY src /tmp/src

RUN cd /tmp && npm run build




FROM node:16-alpine

COPY package*.json /tmp/

RUN cd /tmp && npm install --omit=dev




FROM node:16-alpine

RUN mkdir -p /usr/src/app

COPY --from=1 /tmp/package*.json /usr/src/app/

COPY --from=1 /tmp/node_modules /usr/src/app/node_modules

COPY --from=0 /tmp/dist/ /usr/src/app/

WORKDIR /usr/src/app

EXPOSE 3000

CMD npm run start:staging