import { Module } from '@nestjs/common';
import { EuCronService } from './eu-cron.service';
import { EuCronController } from './eu-cron.controller';
import { UserRepository } from '../update-paid-orders/repository/user.repository copy';
import { OrderRepository, PaymentRepository } from '../update-paid-orders/repository';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { config } from 'convict-config';
import { CommonModule } from '@mycimencam-cron/common/common.module';

@Module({
  controllers: [EuCronController],
  providers: [EuCronService, UserRepository, OrderRepository, PaymentRepository],
  imports: [
    ClientsModule.register([
      { name: "JDE", transport: Transport.TCP, options: { host: config.get('jde.host'), port: config.get('jde.port') } },
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
      { name: "PAYMENT", transport: Transport.TCP, options: { host: config.get('paymentMicroService.host'), port: config.get('paymentMicroService.port') } },
    ]),
    CommonModule

  ]
})
export class EuCronModule { }
