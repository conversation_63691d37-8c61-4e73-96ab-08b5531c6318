import { Module } from '@nestjs/common';
import { ClientStockService } from './client-stock.service';
import { ClientStockController } from './client-stock.controller';
import { ClientStockRepository } from './repository';

@Module({
  controllers: [ClientStockController],
  providers: [ClientStockService, ClientStockRepository],
  exports: [ClientStockService, ClientStockRepository]
})
export class ClientStockModule {}
