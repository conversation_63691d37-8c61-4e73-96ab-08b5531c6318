import { Test, TestingModule } from '@nestjs/testing';
import { MomoCronService } from './momo-cron.service';

describe('MomoCronService', () => {
  let service: MomoCronService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [MomoCronService],
    }).compile();

    service = module.get<MomoCronService>(MomoCronService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
