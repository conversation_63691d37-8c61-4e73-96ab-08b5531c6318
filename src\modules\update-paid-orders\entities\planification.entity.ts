import { Store } from "./store.entity";
import { Product } from "./product.entity";
import { Packaging } from "./packaging.entity";
import { CompanyCategory } from "./company.entity";

export class Planification {
    _id?: string;
    id: number;
    title: string;
    start: string;
    end: string;
    data: PlanificationData;
  }
  
  export interface PlanificationData {
    store: Store;
    product: Product;
    quarter: string;
    packaging: Partial<Packaging>
    quantity: number;
    quantityLeft: number;
    categoryShare: [{
      category: CompanyCategory;
      quantity: number;
      quantityLeft: number;
    }];
    clientShare: [{
      company: {
        _id: string;
        name: string;
      };
      quantity: number;
      quantityLeft: number;
    }];
    quartTimes: QuarterTimeShare[];
    start: string;
    end: string;
  }

  export interface QuarterTimeShare {
    quart: {
      value: string,
      quartHours: string
    };
    quantity: number;
    quantityLeft: number;
    periodicity: {
      eventStartDateQuart?: any,
      eventEndDateQuart?: any,
    };
    dispatcherByDay?: DispatchByDate[];
  }

  declare type DispatchByDate = {
    date: number,
    quantity: number
  };