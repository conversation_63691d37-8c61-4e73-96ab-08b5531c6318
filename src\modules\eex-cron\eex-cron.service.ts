import moment from 'moment';
import * as XLSX from 'xlsx';
import { <PERSON>ron } from '@nestjs/schedule';
import { config } from 'convict-config';
import { PinoLogger } from 'nestjs-pino';
import { Inject, Injectable } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { ExpressExchangeRepository } from '../update-paid-orders/repository';
import { StoreDatabaseType } from '@mycimencam-cron/common/services/common.entity';

@Injectable()
export class EexCronService {

  constructor(
    private readonly logger: PinoLogger,
    private eexRepository: ExpressExchangeRepository,
    @Inject('QUEUE') private readonly queueClient: ClientProxy,

  ) {
  }

  @Cron(config.get('eexCronExecutionTime'), { disabled: config.get('activeCronFOrNotifyEEX') })
  async sendDailyReportEEXtoCimencamAndExpressExchange() {
    this.logger.info(`=|=|=| Start Cron sendDailyReportEEXtoCimencamAndExpressExchange for${config.get('systemName')} |=|=|=\n
      variable: ACTIVE_CRON_NOTIFY_EEX_MCM\n`);
    try {
      const date = moment().subtract(1, 'days').format('DD/MM/YYYY');
      const xslsxData = await this.generateTransactionsExportData();
      this.queueClient.emit('eex_daily_report',
        {
          content: {
            contentType: xslsxData && 'contentType' in xslsxData ? xslsxData?.contentType : '',
            fileContent: xslsxData && 'fileContent' in xslsxData ? xslsxData?.fileContent?.toString("base64") : Buffer.from('')
          },
          date: date
        });
    } catch (error) {
      this.logger.error(`send daily report exx transactions failed \n ${error.stack}`);
      return error;
    }
  }

  async generateTransactionsExportData( fields?: any,) {
    try {
      const start = fields?.start ? moment(fields?.start, 'DD/MM/YYYY') : moment().subtract(1, 'days');
      const end = fields?.end ? moment(fields?.end, 'DD/MM/YYYY') : moment().subtract(1, 'days');
      const query = { transactionDate: { $gte: start.startOf('day').valueOf(), $lte: end.endOf('day').valueOf() } };
      const transactions = await this.eexRepository.findAll({ filter: query }) as unknown as any[];

      const excelArrayBuffer = this.generateTransactionExportXlsx(transactions || []);
      const buffer = Buffer.from(excelArrayBuffer as ArrayBuffer);

      return { contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', fileContent: buffer };
    } catch (error) {
      console.error(`Error generating transaction export data: ${error?.message}`);
      return new Error('Error generating transaction export data');
    }
  }

  generateTransactionExportXlsx(transactions: any[]): ArrayBuffer | Error {
    try {
      // Format Excel file columns headers;
      if (transactions && transactions.length > 0) {
        const excelData: any[][] = [];
        excelData.push(['N°', 'TransactionId', 'MONTANT (XAF)', 'DATE ET HEURE', 'MOTIF', 'STATUT', 'COMPAGNIE', 'CLIENT EEX', 'TELEPHONE CLIENT EEX', 'AGENCE', 'VILLE',]);

        let num = 0;
        transactions.forEach(transaction => {

          const elt = [
            ++num,
            transaction?.transactionId || '',
            transaction?.amount || '',
            transaction?.transactionDate
              ? moment(transaction?.transactionDate).format('DD/MM/YYYY HH:mm')
              : '',
            transaction?.transactionReason,
            transaction?.status === 'ACCEPTE' ? 'ACCEPTE' : 'ANNULE',
            transaction?.company?.name || '',
            transaction?.user?.['name'] || '',
            transaction?.user?.tel || '',
            transaction?.address?.agency || '',
            transaction?.address?.city || ''

          ];
          excelData.push(elt);
        });

        const ws = XLSX.utils.aoa_to_sheet(excelData);
        const wb = XLSX.utils.book_new();
        ws['!cols'] = [
          { wch: 3 }, { wch: 12 }, { wch: 15 }, { wch: 16 }, { wch: 20 },
          { wch: 10 }, { wch: 20 }, { wch: 20 }, { wch: 20 }, { wch: 20 },
          { wch: 20 }
        ];

        XLSX.utils.book_append_sheet(wb, ws, `export_${new Date().getTime()}`);
        return XLSX.write(wb, { type: 'array', bookSST: true, compression: true });
      }
      throw new Error('No transactions provided');
    } catch (error) {
      this.logger.error(`Excel transaction EEX list export generation failed \n ${error.stack}`);
      return error;
    }
  }

}
