import { Inject, Injectable } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Cron, CronExpression } from '@nestjs/schedule';
import { OrderRepository, PaymentRepository, PlanificationRepository } from './repository';
import { CartItem, Order, OrderStatus, Planification } from './entities';
import { PinoLogger } from 'nestjs-pino/PinoLogger';
import { OperatorTransactionStatus, Payment, PaymentMode } from '@mycimencam-cron/types';
import moment from 'moment';
import EventEmitter2 from 'eventemitter2';
import { CommonService } from '@mycimencam-cron/common';
import { StoreDatabaseType } from '@mycimencam-cron/common/services/common.entity';
import { config } from 'convict-config';
import { CompanyCategory } from './entities/company.entity';
import { ForecastRepository } from './repository/forecast.repository';

@Injectable()
export class UpdatePaidOrdersService {
  constructor(
    private eventEmitter: EventEmitter2,
    private readonly logger: PinoLogger,
    private commonSrv: CommonService,
    private readonly orderRepository: OrderRepository,
    private readonly planificationRepository: PlanificationRepository,
    private readonly paymentRepository: PaymentRepository,
    private readonly forecastRepository: ForecastRepository,

    @Inject('QUEUE') private readonly queueClient: ClientProxy
  ) { }

  @Cron(CronExpression.EVERY_5_MINUTES, { disabled: config.get('activeCron') })
  async handleUpdatePaidOrder() {
    this.logger.info(`=|=|=| Start Cron handleUpdatePaidOrder for ${config.get('systemName')} |=|=|=
            \n variable: ACTIVE_CRON_CREATE_ORDERS_MCM\n`);

    const orders = await this.getCreatedOrders(100);
    await this.handleUpdatePaidOrderCommon(orders);
  }

  async handleUpdatePaidOrderCommon(orders: any ) {
    try {
      this.logger.info(`There as ${orders?.length} orders pay to uptades`);

      for (const order of orders) {
        const nbrTries = order?.nbrTries ? order?.nbrTries + 1 : 1;
        await this.orderRepository.update({ _id: order?._id }, { "nbrTries": nbrTries });

        const paymentTransaction = await this.getOrderPaymentTransaction(order);

        this.logger.info(`Start process to updating order with appRefference: ${order.appReference} and payment _id: ${paymentTransaction?._id + '-' + paymentTransaction?.status}`);

        if ([OperatorTransactionStatus.SUCCESSFUL, 'SUCCESSFULL'].includes(paymentTransaction?.status)) {
          if (order.payment.mode.id === PaymentMode.ORANGE_MONEY)
            return await this.eventEmitter.emitAsync('update_order_om', paymentTransaction?.status, order)

          if (order.payment.mode.id === PaymentMode.MOBILE_MONEY)
            return await this.eventEmitter.emitAsync('update_order_momo', paymentTransaction, paymentTransaction?.order?._id)

          const res = await this.orderRepository.update({ _id: order?._id }, { status: OrderStatus.PAID });
          if (res?.modifiedCount > 0) {
            await this.integrateStockThresholdNotifications(order);
            this.logger.info(`Status de la commande ${order.appReference} mis à jour à payée.`);
            this.queueClient.emit('payment_successful', this.commonSrv.EventData(order));
          }
        }
      }
    } catch (error) {
      this.logger.info(`Error while updating order.`);
      return error;
    }
  }
  @Cron("50 23 * * 0", { disabled: config.get('activeCron') })
  async handleOrdersWithNoJdeNbers() {
    this.logger.info(`=|=|=| Start Cron handleOrdersWithNoJdeNbers for ${config.get('systemName')} |=|=|=
            \n variable: ACTIVE_CRON_CREATE_ORDERS_CIMFIG\n`);

    await this.handleOrdersWithNoJdeNbersCommon();
  }

  async handleOrdersWithNoJdeNbersCommon() {
    try {
      const lastSaturday = this.getPreviousWeekSaturday(new Date());
      const currenWeekFriday = this.getCurrentWeekFriday(new Date());
      const query = {
        'erpReference': { $exists: 0 },
        'created_at': {
          $exists: 1,
          $gte: lastSaturday,
          $lte: currenWeekFriday
        },
        status: OrderStatus.PAID,
        'enable': true
      }
      const orders = await this.orderRepository.findAll({ filter: query }) as Order[];

      for (const order of orders) {
        await this.orderRepository.update({ _id: order?._id }, { enable: false });
        this.logger.info(`La commande ${order.appReference} à été archivée.`);
      }
    } catch (error) {
      this.logger.info(`Error while archiving orders.`);
      return error;
    }
  }

  private getPreviousWeekSaturday(currentDate: Date): number {
    const today = currentDate.getDay();
    const daysAgo = today === 6 ? 1 : today + 1;
    const lastSaturday = new Date(currentDate);
    lastSaturday.setDate(currentDate.getDate() - daysAgo);
    lastSaturday.setHours(0, 0, 0, 0);

    return moment(lastSaturday).valueOf();
  }

  private getCurrentWeekFriday(currentDate: Date): number {
    const today = currentDate.getDay();
    const daysAhead = today === 5 ? 7 : (5 - today + 7) % 7;
    const currentWeekFriday = new Date(currentDate);
    currentWeekFriday.setDate(currentDate.getDate() + daysAhead);
    currentWeekFriday.setHours(23, 59, 59, 999);

    return moment(currentWeekFriday).valueOf();
  }

  private async getCreatedOrders(limit: number): Promise<Order[]> {
    const query = {
      status: OrderStatus.CREATED,
      $or: [{ nbrTries: { $lte: config.get('nbrTries') } }, { nbrTries: { $exists: 0 } }],
      "dates.created": { $gte: moment().startOf('day').valueOf(), $lte: moment().endOf('day').valueOf() }
    };

    return await this.orderRepository.findAll({ filter: query, limit }) as unknown as Order[];
  }

  private async getOrderPaymentTransaction(order: Order): Promise<Payment> {
    return await this.paymentRepository.findOne({ filter: { "order._id": order?._id?.toString() } }) as unknown as Payment;
  }

  async integrateStockThresholdNotifications(order: Order) {

    const result = await this.planificationRepository.getCustomPlanifications(order)
    if (result?.length > 0) {
      await this.getOrderPlanification(result, order)
    } else {
      await this.createExtractForecast(order)
    }
  }

  async getOrderPlanification(planifications: any[], order: Order) {
    const results = [];
    const processedIds = new Set(); // Track processed planifications to avoid double decrement

    // Group planifications by unique combination of product, store, and packaging
    const grouped = new Map<string, any[]>();

    for (const plan of planifications) {
      const key = `${plan?.data?.product?._id}-${plan?.data?.store?._id}-${plan?.data?.packaging?._id}`;
      if (!grouped.has(key)) {
        grouped.set(key, []);
      }
      grouped.get(key).push(plan);
    }

    for (const [key, group] of grouped) {
      // Check if group matches any product in the order
      const matchingItem = order?.cart?.items?.find(item =>
        group.some(plan =>
          item?.product?._id?.toString() === plan?.data?.product?._id?.toString() &&
          order?.cart?.store?._id?.toString() === plan?.data?.store?._id?.toString() &&
          order?.cart?.packaging?._id?.toString() === plan?.data?.packaging?._id?.toString()
        )
      );

      if (matchingItem) {
        // If multiple match order, pick the latest one

        const validPlans = group?.filter(plan => {
          const endDate = new Date(plan?.data?.end);
          const today = new Date();
          return endDate >= today;
        });
        if (validPlans?.length > 0) {
          const latest = validPlans?.reduce((min, current) => {
            const minStart = new Date(min?.data?.start);
            const currStart = new Date(current?.data?.start);

            if (currStart < minStart) return current;

            if (currStart.getTime() === minStart.getTime()) {
              const minCreated = new Date(min?.created_at);
              const currCreated = new Date(current?.created_at);
              return currCreated < minCreated ? current : min;
            }

            return min;
          });

          if (!processedIds?.has(latest?._id?.toString())) {
            processedIds?.add(latest?._id?.toString());
            const result = await this.stockDecrementationAndNotification(latest, order);
            results?.push(result);
          }
        }
      } else {
        // Process each non-matching planification one by one
        for (const plan of group) {
          if (!processedIds?.has(plan?._id?.toString())) {
            processedIds?.add(plan?._id?.toString());
            const result = await this.stockDecrementationAndNotification(plan, order);
            results?.push(result);
          }
        }
      }
    }

    return results;
  }


  async stockDecrementationAndNotification(planification: Planification, order: Order) {
    const matchingItem = order?.cart?.items?.find(item => item?.product?._id === planification?.data?.product?._id);
    const matchingClientIndex = planification?.data?.clientShare?.findIndex(share => share?.company?._id?.toString() === order?.company?._id?.toString());
    const matchingCategoryIndex = planification?.data?.categoryShare?.findIndex(share => share?.category === order?.company?.category);
    if (!matchingItem) {
      return { ...planification, message: "No matching product found" };
    }

    await this.decrementClientShareAndNotify(planification, matchingClientIndex, matchingItem, order);
    await this.decrementCategoryShareAndNotify(planification, matchingCategoryIndex, matchingItem, order);
    if ((matchingClientIndex == -1) && (matchingCategoryIndex == -1)) {
      await this.decrementGeneralShareAndNotify(planification, matchingItem, order);
    }
  }

  async decrementCategoryShareAndNotify(planification: Planification, matchingCategoryIndex: number, matchingItem: CartItem, order: Order) {
    if (planification?.data?.categoryShare.length > 0 && (matchingCategoryIndex != -1)) {

      planification.data.categoryShare[matchingCategoryIndex].quantityLeft -= (matchingItem?.quantity / order?.cart?.packaging?.unit?.ratioToTone);
      const res = await this.planificationRepository.update({ _id: planification?._id }, { ...planification })
      if (res?.acknowledged) {
        const percentageLeft = (planification?.data?.categoryShare[matchingCategoryIndex]?.quantityLeft / planification?.data?.categoryShare[matchingCategoryIndex]?.quantity) * 100;
        if (percentageLeft < 10) {
          const message = `Bonjour Mme/M.\nLa quantité du produit ${planification?.data?.product?.label} planifier pour la categorie ${CompanyCategory[planification?.data?.categoryShare[matchingCategoryIndex]?.category]} est inférieure à 10% de la Quantité initiale.\nQuantité initiale: ${planification?.data?.categoryShare[matchingCategoryIndex].quantity}T, Quantité restante: ${planification?.data?.categoryShare[matchingCategoryIndex].quantityLeft}T.\nMerci de prendre les mesures nécessaires.`;
          this.queueClient.emit('minimum_stock', {
            category: CompanyCategory[planification?.data?.categoryShare[matchingCategoryIndex]?.category],
            quantity: planification?.data?.categoryShare[matchingCategoryIndex]?.quantity,
            product_label: planification?.data?.product?.label,
            quantityLeft: planification?.data?.categoryShare[matchingCategoryIndex]?.quantityLeft
          });

          return this.queueClient.emit('sms_received', {
            receiver: ['development', 'staging'].includes(config.get("env")) ? config.get("telServiceClient") : config.get("telServiceClient"),
            message: message
          });
        }
      } else {
        await this.createExtractForecast(order)
      }
    }
  }

  async decrementClientShareAndNotify(planification: Planification, matchingClientIndex: number, matchingItem: CartItem, order: Order) {
    if (planification?.data?.clientShare?.length > 0 && (matchingClientIndex != -1)) {
      planification.data.clientShare[matchingClientIndex].quantityLeft -= (matchingItem?.quantity / order?.cart?.packaging?.unit?.ratioToTone);
      const res = await this.planificationRepository.update({ _id: planification?._id }, { ...planification })
      if (res?.acknowledged) {
        const percentageLeft = (planification?.data?.clientShare[matchingClientIndex]?.quantityLeft / planification?.data?.clientShare[matchingClientIndex]?.quantity) * 100;
        if (percentageLeft < 10) {
          const message = `Bonjour Mme/M.\nLa quantité du produit ${planification?.data?.product?.label} planifier pour le client ${planification?.data?.clientShare[matchingClientIndex]?.company?.name} est inférieure à 10% de la Quantité initiale.\nQuantité initiale: ${planification?.data?.clientShare[matchingClientIndex]?.quantity}T, Quantité restante: ${planification?.data?.clientShare[matchingClientIndex]?.quantityLeft}T.\nMerci de prendre les mesures nécessaires.`;

          this.queueClient.emit('minimum_stock', {
            client_name: planification?.data?.clientShare[matchingClientIndex]?.company?.name,
            quantity: planification?.data?.categoryShare[matchingClientIndex]?.quantity,
            product_label: planification?.data?.product?.label,
            quantityLeft: planification?.data?.categoryShare[matchingClientIndex]?.quantityLeft
          });

          return this.queueClient.emit('sms_received', {
            receiver: ['development', 'staging'].includes(config.get("env")) ? config.get("telServiceClient") : config.get("telServiceClient"),
            message: message
          });
        }
      } else {
        await this.createExtractForecast(order)
      }
    }
  }

  async decrementGeneralShareAndNotify(planification: Planification, matchingItem: CartItem, order: Order) {
    planification.data.quantityLeft -= (matchingItem?.quantity / order?.cart?.packaging?.unit?.ratioToTone);
    this.updatePlanificationQuantities(planification, order);
    const res = await this.planificationRepository.update({ _id: planification?._id }, { ...planification });
    if (res?.acknowledged) {
      const percentageLeft = (planification?.data?.quantityLeft / planification?.data?.quantity) * 100;
      if (percentageLeft < 10) {
        const message = `Bonjour Mme/M.\nLa quantité de ${planification?.data?.product?.label} est inférieure à 10% de la Quantité initiale.\nQuantité initiale: ${planification?.data?.quantity}T, Quantité restante: ${planification?.data?.quantityLeft}T.\nMerci de prendre les mesures nécessaires.`;

        this.queueClient.emit('minimum_stock', {
          quantity: planification?.data?.quantity,
          product_label: planification?.data?.product?.label,
          quantityLeft: planification?.data?.quantityLeft
        });

        this.queueClient.emit('sms_received', {
          receiver: ['development', 'staging'].includes(config.get("env")) ? config.get("telServiceClient") : config.get("telServiceClient"),
          message: message
        });
        return;
      }
    } else {
      await this.createExtractForecast(order)
    }
    return res;

  }

  async updatePlanificationQuantities(planification: Planification, order: Order) {
    order?.removals?.forEach(removal => {
      // Check that this removal is for the same product as the planification
      const planProductId = planification?.data?.product?._id?.toString();
      const removalProductId = removal?.itemId?.toString();

      if (planProductId !== removalProductId) return; // Skip if not matching

      // Now safe to loop over schedules
      removal?.schedules?.forEach(schedule => {
        const quartTime = schedule?.quartTime;
        const removalDate = moment(schedule?.removalDate).startOf('day');
        const decrementValue = Number(schedule?.nbrTonnage) * (schedule?.nbrTruck ?? 1);

        // Step 1: Get all matching quartTimes by quart.value, keeping their original indexes
        const matchingQuartTimes = planification?.data?.quartTimes
          ?.map((qt, index) => ({ qt, index }))
          ?.filter(({ qt }) => qt?.quart?.value === quartTime);

        // Step 2: Find the one that has a dispatcherByDay matching the removal date
        for (const { qt, index } of matchingQuartTimes) {
          const dispatchIndex = qt?.dispatcherByDay?.findIndex(dd =>
            moment(dd?.date).isSame(removalDate, 'day')
          );

          if (dispatchIndex !== -1 && dispatchIndex !== undefined) {
            // Step 3: Decrement both quantityLeft and dispatcherByDay quantity
            planification.data.quartTimes[index].quantityLeft -= decrementValue;
            planification.data.quartTimes[index].dispatcherByDay[dispatchIndex].quantity -= decrementValue;
            break; // Exit loop once matched and updated
          }
        }
      });
    });
  }
  async createExtractForecast(order: Order) {
    // Initialize the forecas
    const { store, packaging, items } = order?.cart;

    const forecast = {
      user: order?.user,
      company: order?.company,
      appRefence: order?.appReference,
      store,
      payment: order?.payment,
      packaging,
      items,
      removals: order.removals,
      status: 100
    }

    return await this.forecastRepository.create(forecast)

  }

  // async updateForecasts(forecasts: any[], order: Order) {
  //   const results = [];
  //   for (const object of forecasts) {
  //     const result = await this.makeUpdate(object, order);
  //     results.push(result);
  //   }
  //   return results;
  // }

  // async makeUpdate(forecast, order) {
  //   if (forecast?.store?._id === order?.cart?.store?._id && 
  //     forecast?.packaging?._id === order?.cart?.packaging?._id) {

  //   // Create a map of order items by product ID for quick lookup
  //   const orderItemsMap = new Map();
  //   order.cart.items.forEach(item => {
  //     orderItemsMap.set(item.product?._id, item.quantity);
  //   });

  //   // Iterate over forecast items and update quantities
  //   forecast.items.forEach(forecastItem => {
  //     const orderQuantity = orderItemsMap.get(forecastItem.product._id);
  //     if (orderQuantity !== undefined) {
  //       forecastItem.quantity += orderQuantity;
  //     }
  //   });
  // }

  // return   await this.forecastRepository.update({_id : forecast?._id }, forecast) 

  // }



}