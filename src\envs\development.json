{"port": "3005", "db": {"host": "127.0.0.1:27017", "name": "mycimencam-v3"}, "db_cimfig": {"host": "127.0.0.1:27017", "name": "mycimfig"}, "payment": {"mtn": {"baseUrl": "https://proxy.momoapi.mtn.com", "user": "a51b77c4-a0b7-4d5d-a8c8-abdca61af410", "password": "e2a9f7bb8aea4e2798cd6c50135fbbd3", "subscription_Key": "3f867eadea1e4d66a4a0f5b0a47432bd", "enviroment": "mtncameroon", "currency": "XAF"}, "orange": {"baseUrl": "https://api-s1.orange.cm", "baseUri": "https://apiw.orange.cm/omcoreapis/1.0.2/mp/paymentstatus", "clientId": "w_aYrRBKbxA6HTgXhJ7Rl0IzcJIa", "clientSecret": "Oac6C4WK8tSylZ5PcA6tKdZKQnsa", "currency": "XAF", "api_username": "MYCIMENCAMPROD", "api_password": "MYCIMENCAMPROD2021", "lang": "fr", "pin": "0736", "channelUserMsisdn": "696793660", "reference": "CIMENCAM", "notifyUrl": "https://mycimencam.com/api/v3/callback/orange", "redirectUrl": "https://mycimencam.com/home", "cancelUrl": "https://mycimencam.com/home"}}, "queue": {"port": 3003, "host": "localhost"}, "jde": {"port": 3004, "host": "localhost"}, "loadDispatch": {"apiUrl": "http://localhost:3009/api/v1", "login": "loadDispatch_user_mcm", "password": "C9FVCiiKhZ6NjmQZayn1"}, "paymentMicroService": {"port": 3002, "host": "localhost"}, "activeSnitch": true, "telServiceClient": "653199177", "eexCronExecutionTime": "0 09 * * *"}