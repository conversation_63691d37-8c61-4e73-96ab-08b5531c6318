declare type ObjectType<T> = {
    [key: string]: T
}

declare type QueryOptions = {
    filter?: QueryFilter,
    projection?: QueryProjection,
    limit?: number,
    offset?: number,
    sort?: string,
    way?: 1 | -1,
}

declare type QueryFilter = ObjectType<any>

declare type QueryProjection = ObjectType<number>

declare type DataId = string | number;

declare type PaymentInfo<T> = {
    [key: 'status' | string]: T | string | number | any;
}

declare type QueryResult = {
    status?: number;
    message?: string;
    data?: any;
}

declare type Address = {
    region?: string;
    city?: string;
    district?: string;
}

declare type Point = {
    validated: number;
    unvalidated: number;
    archived: number;
}

declare type Snitch = {
    action: string;
    user: any;
    newData: any;
    collectionName: string;
    oldData?: any;
    created_at: number;
    description: string;
}

declare type QueueData = {
    subject: string,
    receiver: string,
    body: string,
    date: Date,
    cc?: string,
    attachments?: string
}

declare type QueueItem = {
    type: string,
    proc: Date,
    data: QueueData,
    priority: number;
}

declare type QueueOptions = {
    priority?: number;
    cc?: string;
    delay?: Date | number;
}

declare type AppAuthorization = ObjectType<string[]>

declare type PaginationParam = 'offset' | 'limit';

declare type SortingParam = 'sort' | 'way';

declare type FilterParam = (PaginationParam | SortingParam) extends keyof QueryOptions ? PaginationParam | SortingParam : never;

declare type TemplateData = ObjectType<string | number>

declare type getAllResult = {
    data: ObjectType<any>[],
    count: number
}
