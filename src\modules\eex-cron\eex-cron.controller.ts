import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { EexCronService } from './eex-cron.service';
import { CreateEexCronDto } from './dto/create-eex-cron.dto';
import { UpdateEexCronDto } from './dto/update-eex-cron.dto';

@Controller('eex-cron')
export class EexCronController {
  constructor(private readonly eexCronService: EexCronService) {}

  // @Post()
  // create(@Body() createEexCronDto: CreateEexCronDto) {
  //   return this.eexCronService.create(createEexCronDto);
  // }

  // @Get()
  // findAll() {
  //   return this.eexCronService.findAll();
  // }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.eexCronService.findOne(+id);
  // }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateEexCronDto: UpdateEexCronDto) {
  //   return this.eexCronService.update(+id, updateEexCronDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.eexCronService.remove(+id);
  // }
}
