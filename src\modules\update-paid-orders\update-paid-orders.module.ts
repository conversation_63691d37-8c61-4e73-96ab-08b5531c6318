import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ScheduleModule } from '@nestjs/schedule';
import { CompanyRepository, ForecastRepository, OrderRepository, PaymentRepository, PlanificationRepository, ReloadBalanceRepository } from './repository';
import { UpdatePaidOrdersService } from './update-paid-orders.service';
import { UserRepository } from './repository/user.repository copy';
import { config } from 'convict-config';
import { CommonService } from '@mycimencam-cron/common';

@Module({
  imports: [ScheduleModule.forRoot(),
  ClientsModule.register([
    { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
    { name: "JDE", transport: Transport.TCP, options: { host: config.get('jde.host'), port: config.get('jde.port') } },
  ])
  ],
  providers: [UpdatePaidOrdersService, OrderRepository, PaymentRepository, UserRepository, CompanyRepository, PlanificationRepository, ForecastRepository, ReloadBalanceRepository, CommonService],
  exports: [UpdatePaidOrdersService, OrderRepository, PaymentRepository, UserRepository, CompanyRepository, ReloadBalanceRepository, CommonService]
})
export class UpdatePaidOrdersModule { }
