import { config } from "../../convict-config";
import { Db, MongoClient, MongoClientOptions } from "mongodb";

export class DatabaseMongoDB {
    private db!: Db;
    private options!: MongoClientOptions;
    private static instance: DatabaseMongoDB;

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    constructor() { }

    public static getInstance = (): DatabaseMongoDB => DatabaseMongoDB.instance ??= new DatabaseMongoDB();

    async setDatabase(): Promise<Db> {
        if (config.get('db.auth.user') && config.get('db.auth.password')) {
            this.options.auth = {
                username: config.get('db.auth.user'),
                password: config.get('db.auth.password')
            }
        }

        try {
            const connection = await MongoClient.connect(this.getMongoDbURL(), this.options);
            return connection.db();
        } catch (error: unknown) {
            console.error(error);
        }
    }

    async getDatabase(): Promise<Db> {
        return this.db ??= await this.setDatabase();
    }

    private getMongoDbURL(): string {
        return (config.get('db.auth.user') && config.get('db.auth.password'))
            ? `mongodb://${config.get('db.auth.user')}:${config.get('db.auth.password')}@${config.get('db.host')}/${config.get('db.name')}?retryWrites=true&w=majority`
            : `mongodb://${config.get('db.host')}/${config.get('db.name')}?retryWrites=true&w=majority`;
    }
}