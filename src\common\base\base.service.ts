import { convertParams, extractPaginationData, extractSortingData, setResponse } from "../helpers";
import { NotFoundException, InternalServerErrorException } from "@nestjs/common";
import { BaseRepository } from "./base.repository";
import { ServiceInterface } from "../interfaces";
import { Document, WithId } from "mongodb";
import moment from "moment";

export class BaseService implements ServiceInterface {

    constructor(
        protected readonly repository: BaseRepository,
    ) { }

    async create(data: Document): Promise<QueryResult> {
        try {
            data.created_at = moment().valueOf();

            const newDocument = await this.repository.create(data);

            if (!newDocument.insertedId) throw new InternalServerErrorException(`Une erreur s'est produite lors de la création`);

            return setResponse(200, 'Data inserted', newDocument.insertedId);
        } catch (error) {
            console.error(error);
            return error;
        }
    }
    async findAll(query: QueryOptions): Promise<getAllResult> {
        try {
            query = convertParams(query);
            query = extractPaginationData(query);
            query = extractSortingData(query);

            const data = await this.repository.findAll(query ?? null);
            const count = await this.count(query.filter);

            if (typeof count == 'number') { return { data, count }; }

        } catch (error) {
            console.error(error);
            return error;
        }
    }

    async findOne(query: QueryOptions): Promise<WithId<Document>> {
        try {
            const document = await this.repository.findOne(query);

            if (!document) throw new NotFoundException('Aucune donnée trouvée');

            return document;
        } catch (error) {
            console.error(error);
            return error;
        }
    }

    async count(query: QueryFilter): Promise<number | QueryResult> {
        try {
            const numberOfDocuments = await this.repository.count(query);

            return numberOfDocuments;
        } catch (error) {
            console.error(error);
            return error;
        }
    }

    async update(filter: QueryFilter, data: Document): Promise<QueryResult> {
        try {
            const existVerify = await this.repository.findOne({ filter });
            if (!existVerify) throw new NotFoundException(`Aucun élément à modifier`);
            delete data?._id;

            const updatedDocument = await this.repository.update(filter, data);

            if (updatedDocument.acknowledged == false) throw new InternalServerErrorException(`Une erreur s'est produite lors de la modification`);

            return setResponse(200, 'Donnée modifiée avec succès');
        } catch (error) {
            console.error(error);
            return error;
        }
    }
}