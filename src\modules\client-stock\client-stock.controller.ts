import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { ClientStockService } from './client-stock.service';
import { CreateClientStockDto } from './dto/create-client-stock.dto';

@Controller('client-stock')
export class ClientStockController {
  constructor(private readonly clientStockService: ClientStockService) {}

  @Post()
  create(@Body() createClientStockDto: CreateClientStockDto) {
  }


}
