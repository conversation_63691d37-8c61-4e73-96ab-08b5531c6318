import { Packaging } from "@mycimencam-cron/modules/update-paid-orders/entities";
import { Company } from "@mycimencam-cron/modules/update-paid-orders/entities/company.entity";
import { Product } from "@mycimencam-cron/modules/update-paid-orders/entities/product.entity";
import { User } from "@mycimencam-cron/modules/update-paid-orders/entities/user.entity";

export class ClientStock  {
    _id?: string;
    user?: Partial<User>;
    company?: Partial<Company>;
    currentProductInStock?: CartItemClientStock[];
    stockMovements?: StockMovement[];
}

export class StockMovement {
    type?: StockMovementType;
    product?: Partial<Product>;
    quantity?: number;
    date?: number;
    unitPrice?: number;
    packaging?: Partial<Packaging>

}


export enum StockMovementType {
    ENTRY = "entry",
    EXIT = "exit",
}


export declare type CartItemClientStock = {
    product?: Partial<Product>;
    quantity?: number;
    threshold?:number;
    unitPrice?: number;
    packaging?: Partial<Packaging>
}
