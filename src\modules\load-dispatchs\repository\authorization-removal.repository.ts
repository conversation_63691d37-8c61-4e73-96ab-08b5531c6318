import { BaseRepository } from "@mycimencam-cron/common";

export class Authorization_RemovalRepository extends BaseRepository {

    constructor() {
        super();
        this.collectionName = 'authorization_removals';
    }

    async replaceAuthRemoval(data: any) {
        if ('_id' in data) delete data?._id;

        return (await this.getCollection()).updateOne({ LoadNumber: data.LoadNumber }, { $set: data }, { upsert: true });
    }
}