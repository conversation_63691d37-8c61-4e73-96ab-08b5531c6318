import { Test, TestingModule } from '@nestjs/testing';
import { EuCronController } from './eu-cron.controller';
import { EuCronService } from './eu-cron.service';

describe('EuCronController', () => {
  let controller: EuCronController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EuCronController],
      providers: [EuCronService],
    }).compile();

    controller = module.get<EuCronController>(EuCronController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
