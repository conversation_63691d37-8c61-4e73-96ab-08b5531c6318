import { Address } from "cluster";
import { CompanyEmployee, User } from "./user.entity";

export class Company {
    _id?: string;
    address?: Address;
    name?: string;
    defaultStore?: string;
    afrilandKey?: string;
    nui?: string;
    rccm?: string;
    tel?: string;
    erpShipToDesc?: string;
    erpSoldToId?: number;
    erpShipToId?: number;
    precompteRate?: PrecompteRate;
    category?: CompanyCategory;
    q1Enabled?: boolean;
    annualOrderEnabled?: boolean;
    users?: CompanyEmployee[];
    enable?: boolean;
    create_at?: number;
    updateAt?: number;
    associatedCommercial?: Partial<User>;
    points?:number;
}

export enum CompanyCategory {
    Binastore = 101,
    Distributor = 102,
    BTP = 103,
    Enseigne = 104,
    Export = 105
}

export enum PrecompteRate {
    Simple = 0.02,
    Real = 0.05,
    withholding = 0.1
}