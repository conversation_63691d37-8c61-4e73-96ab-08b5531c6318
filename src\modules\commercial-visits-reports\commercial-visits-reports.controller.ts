import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { CommercialVisitsReportsService } from './commercial-visits-reports.service';
import { CreateCommercialVisitsReportDto } from './dto/create-commercial-visits-report.dto';
import { UpdateCommercialVisitsReportDto } from './dto/update-commercial-visits-report.dto';

@Controller('commercial-visits-reports')
export class CommercialVisitsReportsController {
  constructor(private readonly commercialVisitsReportsService: CommercialVisitsReportsService) {}
}
