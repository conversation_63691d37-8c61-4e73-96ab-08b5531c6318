{"port": "3000", "host": "cron-api", "db": {"host": "mongodb", "name": "mycimencam-v3"}, "payment": {"mtn": {"baseUrl": "https://proxy.momoapi.mtn.com", "user": "a51b77c4-a0b7-4d5d-a8c8-abdca61af410", "password": "e2a9f7bb8aea4e2798cd6c50135fbbd3", "subscription_Key": "3f867eadea1e4d66a4a0f5b0a47432bd", "enviroment": "mtncameroon", "currency": "XAF"}, "orange": {"baseUrl": "https://api-s1.orange.cm", "baseUri": "https://apiw.orange.cm/omcoreapis/1.0.2/mp/paymentstatus", "clientId": "w_aYrRBKbxA6HTgXhJ7Rl0IzcJIa", "clientSecret": "Oac6C4WK8tSylZ5PcA6tKdZKQnsa", "currency": "XAF", "api_username": "MYCIMENCAMPROD", "api_password": "MYCIMENCAMPROD2021", "lang": "fr", "pin": "0736", "channelUserMsisdn": "696793660", "reference": "CIMENCAM", "notifyUrl": "https://mycimencam.com/api/v3/callback/orange", "redirectUrl": "https://mycimencam.com/home", "cancelUrl": "https://mycimencam.com/home"}}, "queue": {"port": 3000, "host": "queue-api"}, "jde": {"port": 3000, "host": "jde-api"}, "loadDispatch": {"apiUrl": "https://jde.mycimencam.com/api/v1", "login": "loadDispatch_user_mcm", "password": "GG4A&LK#RtzA2TAZQy&deZnv"}, "paymentMicroService": {"port": 3000, "host": "payment-api"}, "telServiceClient": "654900000", "eexCronExecutionTime": "0 09 * * *", "activeSnitch": false, "activeCronCheckEUPayment": false, "paymentRequestForReloadBalanceInJDE": false, "requestForBlJDE": false, "requestForRetrievementJDE": false, "activeCronCheckMtnPayment": false, "activeCronCheckOrangePayment": false, "activeCron": false, "activeCronCreateOrders": false, "activeCronFOrNotifyEEX": false, "activeCronForBl": false, "activeCronSendCommercialReportNotif": false, "systemName": "MCM", "nbrTries": 30, "concurrency": 10}