import { Modu<PERSON> } from '@nestjs/common';
import { EexCronService } from './eex-cron.service';
import { EexCronController } from './eex-cron.controller';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { config } from 'convict-config';
import { CommonModule } from '@mycimencam-cron/common/common.module';
import { ExpressExchangeRepository } from '../update-paid-orders/repository';

@Module({
  controllers: [EexCronController],
  providers: [EexCronService, ExpressExchangeRepository],
  imports: [
    ClientsModule.register([
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
    ]),
    CommonModule
  ],

})
export class EexCronModule { }
