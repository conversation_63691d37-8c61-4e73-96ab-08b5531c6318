import { CompanyRepository } from "@mycimencam-cron/modules/update-paid-orders/repository";
import { Order } from "@mycimencam-cron/modules/update-paid-orders/entities";
import { PaymentMode, keyEventJDE } from "@mycimencam-cron/types";
import { ClientProxy } from "@nestjs/microservices";
import { Inject, Injectable } from "@nestjs/common";
import { PinoLogger } from "nestjs-pino";
import { config } from 'convict-config';
import { lastValueFrom } from "rxjs";
import { ObjectId } from "mongodb";

@Injectable()
export class CommonService {
    constructor(
        @Inject('JDE') private readonly JDEService: ClientProxy,
        private readonly logger: PinoLogger,
    ) { }

    async VerifyPointEligibility(paymentModeId: PaymentMode, order: Order, companyRepo: CompanyRepository) {
        try {
            const balance: any = await lastValueFrom(this.JDEService.send({ cmd: keyEventJDE.GET_BALANCE }, this.EventData({ ...order?.company })));
            if ([PaymentMode.MOBILE_MONEY, PaymentMode.ORANGE_MONEY, PaymentMode.M2U, PaymentMode.AFRILAND, PaymentMode.VISA].includes(paymentModeId)
                && (+balance?.Creditlimit > 0 && +balance?.OpenorderAmount > 0)) {
                const query = { filter: { _id: new ObjectId(order?.company?._id?.toString()) } }

                const company = await companyRepo.findOne({ ...query, projection: { 'points': 1 } });
                await companyRepo.update(query?.filter, { points: (company?.points || 0) + 1 });
            }
        } catch (error) {
            this.logger.info(`Error during VerifyPointEligibility order: ${JSON.stringify(error)}.`);
        }
    }

    EventData(entryData: any) {
        const data = {
            data: entryData,
            metaData: config.get('systemName')
        };
        this.logger.info(`data to send in event ${JSON.stringify(data)}`);
        return data;
    }

}