import { Module } from '@nestjs/common';
import { config } from 'convict-config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { CommercialVisitsReportsService } from './commercial-visits-reports.service';
import { UpdatePaidOrdersModule } from '../update-paid-orders/update-paid-orders.module';
import { CommercialVisitsReportsController } from './commercial-visits-reports.controller';
import { SalesFeedbackRepository, SalesVisitRepository } from '../update-paid-orders/repository';

@Module({
  imports: [
    UpdatePaidOrdersModule,
     ClientsModule.register([
        { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
      ])
  ],
  controllers: [CommercialVisitsReportsController],
  providers: [CommercialVisitsReportsService, SalesVisitRepository, SalesFeedbackRepository],
})
export class CommercialVisitsReportsModule { }
