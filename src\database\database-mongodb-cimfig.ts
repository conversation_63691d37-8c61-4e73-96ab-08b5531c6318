import { Lo<PERSON> } from "@nestjs/common";
import { Db, MongoClient, MongoClientOptions } from "mongodb";
import { config } from "../../convict-config";

export class MongoDatabaseCimfig {

    private static instance: MongoDatabaseCimfig;

    private options!: MongoClientOptions;
    private db!: Db;

    private constructor(private readonly logger?: Logger) { }

    public static getInstance = (): MongoDatabaseCimfig => MongoDatabaseCimfig.instance ??= new MongoDatabaseCimfig();

    public getDatabase = async (): Promise<Db> => this.db ??= await this.setDatabase();

    private async setDatabase(): Promise<Db> {
        if (config.get(`db_cimfig.auth.user`) && config.get('db_cimfig.auth.password')) {
            this.options.auth = {
                username: config.get('db_cimfig.auth.user'),
                password: config.get('db_cimfig.auth.password')
            }
        }

        try {
            const connection = await MongoClient.connect(this.connectionUrl, this.options);
            return connection.db();
        } catch (error: unknown) {
            this.logger?.error(error);
        }
    }

    private get connectionUrl(): string {
        return (config.get('db_cimfig.auth.user') && config.get('db_cimfig.auth.password'))
            ? `mongodb://${config.get('db_cimfig.auth.user')}:${config.get('db_cimfig.auth.password')}@${config.get('db_cimfig.host')}/${config.get('db_cimfig.name')}?retryWrites=true&w=majority`
            : `mongodb://${config.get('db_cimfig.host')}/${config.get('db_cimfig.name')}?retryWrites=true&w=majority`;
    }
}