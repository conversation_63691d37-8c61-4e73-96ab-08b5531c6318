import { Test, TestingModule } from '@nestjs/testing';
import { CommercialVisitsReportsController } from './commercial-visits-reports.controller';
import { CommercialVisitsReportsService } from './commercial-visits-reports.service';

describe('CommercialVisitsReportsController', () => {
  let controller: CommercialVisitsReportsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CommercialVisitsReportsController],
      providers: [CommercialVisitsReportsService],
    }).compile();

    controller = module.get<CommercialVisitsReportsController>(CommercialVisitsReportsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
