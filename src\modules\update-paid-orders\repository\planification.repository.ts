import { BaseRepository } from "@mycimencam-cron/common";

export class PlanificationRepository extends BaseRepository {

  constructor() {
    super();
  }


  async getCustomPlanifications(order) {
    const productIds = order?.cart?.items?.map(item => item?.product?._id?.toString()) || [];
  
    return (await this.getCollection()).aggregate([
      {
        '$match': {
          'data.store._id': `${order?.cart?.store?._id}`,
          'data.product._id': { '$in': productIds },
          'data.packaging._id': `${order?.cart?.packaging?._id}`,
          'enable': true
        }
      },
      {
        '$project': {
          'data': 1,
          'created_at': 1
        }
      }
    ]).toArray();
  }
  


  

}