import moment from 'moment';
import { lastValueFrom } from 'rxjs';
import { config } from 'convict-config';
import { Inject, Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino/PinoLogger';
import { ClientProxy } from '@nestjs/microservices';
import { CommonService } from '@mycimencam-cron/common';
import { StoreDatabaseType } from '@mycimencam-cron/common/services/common.entity';
import { ErrorCodeStatus, ReloadBalance } from '../update-paid-orders/entities/reload-balance.entity';
import { Order } from '../update-paid-orders/entities'; import { Cron, CronExpression } from '@nestjs/schedule';
import { LoadDispatchDates, OperatorTransactionStatus, TransactionStatus, keyEventJDE } from '@mycimencam-cron/types';
import { CompanyRepository, OrderRepository, ReloadBalanceRepository, RetrievementRepository } from '../update-paid-orders/repository';

@Injectable()
export class JdeCronService {
    isRunningMCM: boolean;
    running: boolean;
    isRunning: boolean;
    isRunningForBL: any;
    private readonly MAX_RETRY_ATTEMPTS = 7;
    private readonly HIGH_RETRY_THRESHOLD = 6;

    constructor(
        private readonly logger: PinoLogger,
        private companyRepo: CompanyRepository,
        private orderRepository: OrderRepository,
        private commonSrv: CommonService,
        private reloadBalanceRepository: ReloadBalanceRepository,
        private readonly retrievementRepository: RetrievementRepository,
        @Inject('JDE') private readonly JDEService: ClientProxy,
        @Inject('QUEUE') private readonly queueClient: ClientProxy,
    ) { }

    @Cron(CronExpression.EVERY_MINUTE, { disabled: config.get('activeCronCreateOrders') })
    async reCreateOrderInJDE() {
        this.logger.info(`=|=|=| Start Cron reCreateOrderInJDE for${config.get('systemName')} |=|=|=
            \n variable: ACTIVE_CRON_CREATE_ORDERS_MCM\n`);
        if (this.isRunning)
            throw new Error('Service already running!');

        this.isRunning = true;
        await this.recreateOrderInJdeCommon();
        this.isRunning = false;
    }

    async recreateOrderInJdeCommon() {
        this.logger.info('Starting reCreateOrderInJdeCommon method');

        const query = {
            $or: [{ status: 300 }, { status: 200 }],
            erpReference: { $exists: false },
            "dates.created": { $gte: moment().startOf('day').valueOf(), $lte: moment().endOf('day').valueOf() }
        };

        try {
            this.logger.info('Fetching orders to recreate in JDE');
            this.isRunning = true;
            const orders = await this.orderRepository.findAll({
                filter: query,
                limit: 20,
                projection: { appReference: 1, retryCountOrder: 1, company: 1, 'cart.amount.TTC': 1 }
            }) as Order[];

            this.logger.info(`There are ${orders?.length ?? 0} orders to recreate in JDE`);

            if (orders && orders?.length > 0) {
                for (const order of orders) {
                    this.logger.info(`Calling JDE microservice for order with appReference: ${order?.appReference}`);
                    await this.manageRetryAttempts(order);
                    this.logger.info(`Sending order ${order?.appReference} to JDE microservice`);
                    this.JDEService.emit(
                        { cmd: keyEventJDE.CREATE_ORDER },
                        this.commonSrv.EventData({ appReference: order?.appReference })
                    );
                }
            } else {
                this.logger.info('No orders to process.');
            }

        } catch (error) {
            this.logger.error(`Error during verification. \n${error?.message} \n ${error?.stack}`);
            this.queueClient.emit('send_error', this.commonSrv.EventData({
                message: error,
                microService: 'CRON',
                methodPath: 'reCreateOrderInJDE'
            }));
        } finally {
            this.logger.info('End of reCreateOrderInJdeCommon method');
            this.isRunning = false;
        }
    }

    private async manageRetryAttempts(order: Order) {
        const currentRetryCount = (order?.retryCountOrder ?? 0) + 1;

        if (currentRetryCount < this.MAX_RETRY_ATTEMPTS) {
            this.logger.info(`Updating retry count for order ${order?.appReference}`);
            await this.orderRepository.update(
                { _id: order?._id },
                { retryCountOrder: currentRetryCount }
            );
        }

        if (currentRetryCount >= this.HIGH_RETRY_THRESHOLD && currentRetryCount < this.MAX_RETRY_ATTEMPTS) {
            this.logger.warn(`Retry count is high (${this.HIGH_RETRY_THRESHOLD}) for order ${order?.appReference}`);
            await this.handleMaxRetryReached(order);
        }

        if (currentRetryCount >= this.MAX_RETRY_ATTEMPTS) {
            this.logger.warn(`Maximum retry attempts (${this.MAX_RETRY_ATTEMPTS}) reached for order ${order?.appReference}. No further updates.`);
        }
    }


    private async handleMaxRetryReached(order: Order): Promise<void> {
        if (!order) {
            this.logger.warn("Missing information to handle retry limit.");
            return;
        }

        const currentTime = moment().format("DD/MM/YYYY HH:mm");
        await this.sendNotifications(order, currentTime);
    }

    private async sendNotifications(order: Order, timestamp: string): Promise<void> {
        if (!this.queueClient) {
            this.logger.warn("Queue client is not configured.");
            return;
        }

        const results = await Promise.allSettled([
            this.sendEmailNotification(order, timestamp),
            this.sendSMSNotification(order, timestamp)
        ]);

        results?.forEach((result, index) => {
            if (result?.status === "rejected") {
                const notificationType = index === 0 ? "email" : "SMS";
                this.logger.error(`Failed to send ${notificationType} notification:`, result?.reason);
            }
        });
    }


    private async sendEmailNotification(order: Order, timestamp): Promise<void> {
        if (!order) {
            this.logger.warn(
                'Order is not defined in sendEmailNotification. Please ensure the "order" parameter is passed correctly. Timestamp: ' + timestamp
            );
            return;
        }

        this.queueClient.emit('order_jde_failed', this.commonSrv.EventData({
            order_number: order?.appReference,
            error_message: "Nombre de tentatives de création de la commande atteint.",
            failure_time: timestamp,
            order_amount: order?.cart?.amount?.TTC
        }));
    }

    private async sendSMSNotification(order: Order, timestamp): Promise<void> {
        const smsReceiverNumber = config.get('smsReceiverNumber');

        if (!smsReceiverNumber) {
            this.logger.warn(
                'Phone number for SMS notification is not configured. Please set "smsReceiverNumber" in the configuration file.'
            );
            return;
        }

        this.queueClient.emit('sms_received', this.commonSrv.EventData({
            receiver: smsReceiverNumber,
            message: `Error creating order ${order.appReference} in JDE at ${timestamp}. Maximum retry attempts reached.`
        }));
    }


    @Cron(CronExpression.EVERY_MINUTE, { disabled: config.get('paymentRequestForReloadBalanceInJDE') })
    async paymentRequestInJDE() {
        this.logger.info(`=|=|=| Start Cron paymentRequestInJDE for${config.get('systemName')} |=|=|=
            \n variable: ACTIVE_CRON_PAYMENT_REQUEST_JDE_MCM\n`);

        await this.paymentRequestInJDECommon();
    }

    async paymentRequestInJDECommon() {
        try {

            const query = {
                status: { $in: [TransactionStatus.PENDING, TransactionStatus.INITIATED, TransactionStatus.CREATED] },
                nbrTries: { $lte: config.get('nbrTries') },
                'paymentResponseJDE.errorCode': { $ne: ErrorCodeStatus.SUCCESSFUL },
                $or: [{ "paymentInfo.status": OperatorTransactionStatus.SUCCESSFUL }, { "paymentInfo.status": 'SUCCESSFULL' }],
            }
            const reloadBalances = await this.reloadBalanceRepository.findAll({ filter: query }) as unknown as ReloadBalance[];
            for (const reloadBalance of reloadBalances) {
                const nbrTries = reloadBalance.nbrTries ? reloadBalance.nbrTries + 1 : 1;
                await this.reloadBalanceRepository.update({ _id: reloadBalance._id }, { "nbrTries": nbrTries })
                this.logger.info(`Calling JDE to send payment for reloadBalance with uniqueReference: ${reloadBalance?.uniqueReference}`)
                await lastValueFrom(this.JDEService.emit({ cmd: keyEventJDE.CREATE_RELOAD_BALANCE }, this.commonSrv.EventData(reloadBalance?._id)));
            }
        } catch (error) {
            return error;
        }
    }

    @Cron(CronExpression.EVERY_MINUTE, { disabled: config.get('paymentRequestForReloadBalanceInJDE') })
    async checkStatusPaymentInJDE() {
        this.logger.info(`=|=|=| Start Cron checkStatusPaymentInJDE for${config.get('systemName')} |=|=|=
            \n variable: ACTIVE_CRON_PAYMENT_REQUEST_JDE_MCM\n`);
        this.checkStatusPaymentInJDECommon()
    }

    async checkStatusPaymentInJDECommon() {
        try {

            const query = {
                status: { $in: [TransactionStatus.PENDING, TransactionStatus.INITIATED, TransactionStatus.CREATED] },
                $or: [{ "paymentInfo.status": OperatorTransactionStatus.SUCCESSFUL }, { "paymentInfo.status": 'SUCCESSFULL' }],
                'paymentResponseJDE.errorCode': { $in: [ErrorCodeStatus.SUCCESSFUL, ErrorCodeStatus.PAYMENT_EXIST] },
                'statusPaymentResponseJDE.errorCode': { $ne: ErrorCodeStatus.SUCCESSFUL },
                // createAt: { $lte: moment().endOf('day').valueOf(), $gte: moment().startOf('day').valueOf() },
            }

            const reloadBalances = await this.reloadBalanceRepository.findAll({ filter: query }) as unknown as ReloadBalance[];
            for (const reloadBalance of reloadBalances) {
                await lastValueFrom(this.JDEService.emit({ cmd: keyEventJDE.CHECK_STATUS_PAYMENT_RELOAD_BALANCE }, this.commonSrv.EventData(reloadBalance?._id)));
            }
        } catch (error) {
            return error;
        }
    }

    @Cron(CronExpression.EVERY_5_MINUTES, { disabled: config.get('requestForBlJDE') })
    async getRetrievementForBlMCM() {
        this.logger.info(`=|=|=| Start Cron getRetrievementForBlMCM for${config.get('systemName')} |=|=|=
            \n variable: ACTIVE_CRON_REQUEST_RETRIEVEMENT_JDE_MCM\n`);
        if (this.isRunningForBL)
            return this.logger.warn(`Cron getRetrievementMCM is already have process, skip this execution`);

        this.isRunningForBL = true;
        await this.getRetrievement();
        this.isRunningForBL = false;
    }

    async getRetrievement() {
        const currentDate = moment().tz('Africa/Douala');

        this.logger.info(`start updating retreivements on ${currentDate.format('YYYY/MM/DD HH:mm:ss')}`);

        const [LoadDispatchDateFrom, LoadDispatchDateTo, LoadDispatchTimeFrom, LoadDispatchTimeTo] = this.getLoadDispatchTimeRange(currentDate);

        const dataDates: LoadDispatchDates = {
            dateFrom: LoadDispatchDateFrom,
            dateTo: LoadDispatchDateTo,
            timeFrom: LoadDispatchTimeFrom,
            timeTo: LoadDispatchTimeTo
        }

        this.logger.info(`Calling micro service JDE To get reitrievmentData with  ${JSON.stringify(dataDates)}`);
        const jdeResponse = await lastValueFrom(this.JDEService.send({ cmd: keyEventJDE.LOAD_DISPATCH_DETAILS }, dataDates));

        this.logger.info(`Response of micro service JDE from get data:  ${JSON.stringify(jdeResponse)}`);

        if (jdeResponse?.cpLifeDispatchDetailsReply instanceof Error) { return; }
        const retreivements = this.parseRetreivements(jdeResponse?.cpLifeDispatchDetailsReply);

        const ticketNumbers = retreivements?.map(elt => +elt?.lifeTicketNumber);

        try {

            this.logger.info(`check existing tickets numbers for this ticketNumbers: \n${JSON.stringify(ticketNumbers)} \n length: ${ticketNumbers?.length}`);

            const retreivementsToInsert = [];

            const query = { filter: { lifeTicketNumber: { $in: ticketNumbers } }, projection: { lifeTicketNumber: 1 } }
            let existingDispatchs = await this.retrievementRepository.findAll(query);
            existingDispatchs = existingDispatchs?.map(elt => elt?.lifeTicketNumber);

            this.logger.info(`existingDispatchs: \n${JSON.stringify(existingDispatchs)} \n length: ${existingDispatchs?.length} existing retreivements found`);

            // if (retreivementsToInsert?.length === 0) {
            //     return this.logger.info(`no retreivements to update`);
            // }

            for (const elt of retreivements) {
                if (!existingDispatchs?.includes(elt?.lifeTicketNumber)) {
                    const { driverName, registrationLicenseNumber, primaryVehicleId,
                        shipTo, soldTo, salesOrderNumber, shipmentNumber, loadNumber,
                        actualShipDate, actualShipTime, lifeTicketNumber, details
                    } = elt;
                    retreivementsToInsert?.push({
                        driverName, registrationLicenseNumber, primaryVehicleId,
                        shipTo, soldTo, salesOrderNumber, shipmentNumber, loadNumber,
                        actualShipDate, actualShipTime, lifeTicketNumber, details
                    });
                }
            }

            this.logger.info(`retreivementsToInsert length: ${retreivementsToInsert?.length} retreivements To Insert`);
            for (const retreivement of retreivementsToInsert) {
                const company = await this.companyRepo.findOne({ filter: { erpSoldToId: +retreivement?.soldTo } });
                retreivement.companyName = company?.name ?? '';
                retreivement.enable ??= true;
                retreivement.created_at ??= moment().valueOf();
            }

            this.logger.info(`inserting ${retreivementsToInsert?.length} new retreivements`);

            const result = await this.retrievementRepository.createMany(retreivementsToInsert);

            this.logger.info(`successful insertion of ${result?.insertedCount} retreivements`);

        } catch (error) {
            this.logger.error(`Error while inserting new retreivements; message: ${error.message} \n ${error.stack}`);
            this.queueClient.emit('send_error', {
                message: error,
                microService: 'CRON',
                methodPath: 'getRetrievementsFromJde'
            });
        }
    }

    // @Cron(CronExpression.EVERY_MINUTE)
    // async getRetrievement() {

    //     if (this.isRunning)
    //         return this.logger.warn(`Cron getRetrievement is already have process, skip this execution`);

    //     this.isRunning = true;

    //     const currentDate = moment().tz('Africa/Douala');

    //     this.logger.info(`start updating retreivements on ${currentDate.format('YYYY/MM/DD HH:mm:ss')}`);

    //     const [LoadDispatchDateFrom, LoadDispatchDateTo, LoadDispatchTimeFrom, LoadDispatchTimeTo] = this.getLoadDispatchTimeRange(currentDate);

    //     const dataDates: LoadDispatchDates = {
    //         dateFrom: LoadDispatchDateFrom,
    //         dateTo: LoadDispatchDateTo,
    //         timeFrom: LoadDispatchTimeFrom,
    //         timeTo: LoadDispatchTimeTo
    //     }

    //     const jdeResponse = await lastValueFrom(this.JDEService.send({ cmd: keyEventJDE.LOAD_DISPATCH_DETAILS }, dataDates));

    //     if (jdeResponse?.cpLifeDispatchDetailsReply instanceof Error) { return; }
    //     const retreivements = this.parseRetreivements(jdeResponse?.cpLifeDispatchDetailsReply);

    //     const tiketNumbers = retreivements?.map(elt => elt?.lifeTicketNumber);

    //     try {

    //         this.logger.info('check existing tikets numbers');

    //         let retreivementsToInsert = [];

    //         let existingDispatchs = await this.retrievementRepository.findAll({ filter: { lifeTicketNumber: { $in: tiketNumbers } }, projection: { lifeTicketNumber: 1 } });
    //         existingDispatchs = existingDispatchs?.map(elt => elt?.lifeTicketNumber);

    //         this.logger.info(`${existingDispatchs?.length} existing retreivements found`);

    //         for (const elt of retreivements) {
    //             if (!existingDispatchs?.includes(elt?.lifeTicketNumber)) {
    //                 const { driverName, registrationLicenseNumber, primaryVehicleId,
    //                     shipTo, soldTo, salesOrderNumber, shipmentNumber, loadNumber,
    //                     actualShipDate, actualShipTime, lifeTicketNumber, details
    //                 } = elt;
    //                 retreivementsToInsert?.push({
    //                     driverName, registrationLicenseNumber, primaryVehicleId,
    //                     shipTo, soldTo, salesOrderNumber, shipmentNumber, loadNumber,
    //                     actualShipDate, actualShipTime, lifeTicketNumber, details
    //                 });
    //             }
    //         }

    //         if (retreivementsToInsert?.length === 0) {
    //             return this.logger.info(`no retreivements to update`);
    //         }

    //         let companies = await this.companyRepo.findAll({});
    //         for (const retreivement of retreivementsToInsert) {
    //             const company = companies?.find(companyItem => companyItem?.erpSoldToId === retreivement?.soldTo);
    //             retreivement.companyName = (company) ? company.name : '';
    //         }

    //         this.logger.info(`inserting ${retreivementsToInsert?.length} new retreivements`);

    //         let insertCount = 0;

    //         for (const retreivement of retreivementsToInsert) {
    //             const result = await this.retrievementRepository.create(retreivement);
    //             if (result) {
    //                 insertCount++;
    //             }
    //         }
    //         this.logger.info(`successfull insertion of ${insertCount} retreivements`);

    //     } catch (error) {
    //         this.logger.error(`Error while inserting new retreivements \n ${error.stack}`);
    //         this.queueClient.emit('send_error', {
    //             message: error,
    //             microService: 'CRON',
    //             methodPath: 'getRetrievementsFromJde'
    //         });
    //     } finally {
    //         this.isRunning = false;
    //     }
    // };

    parseRetreivements(jdeResponse: any) {
        return jdeResponse?.map(elt => {
            if (!elt?.Details || !elt.Details?.ItemNumber) {
                this.logger.debug(`${JSON.stringify(elt)}`);
                return null;
            }
            return {
                driverName: elt?.Header?.DriverName ? elt?.Header?.DriverName.trim() : '',
                registrationLicenseNumber: elt.Header.RegistrationLicenseNumber,
                primaryVehicleId: elt?.Header?.PrimaryVehicleId,
                shipTo: parseInt(elt?.Header?.ShipTo, 10),
                soldTo: parseInt(elt?.Header?.SoldTo, 10),
                salesOrderNumber: parseInt(elt?.Header?.SalesOrderNumber, 10),
                shipmentNumber: parseInt(elt?.Header?.ShipmentNumber, 10),
                loadNumber: parseInt(elt?.Header?.LoadNumber, 10),
                actualShipDate: moment(elt?.Header?.ActualShipDate).valueOf(),
                actualShipTime: elt?.Header?.ActualShipTime,
                lifeTicketNumber: parseInt(elt?.Header?.LIFETicketNumber, 10),
                details: this.getRetreivementItems(elt?.Details)
            };
        });
    }

    getRetreivementItems(data: any) {
        return {
            qtyShipped: data?.QuantityShipped,
            qtyOrdered: data?.QuantityOrdered,
            item: this.getProduct(data?.ItemNumber)
        }
    }

    getProduct(code: any) {
        code = code?.slice(0, 3);
        const mapping = {
            'ROB': {
                label: 'ROBUST',
                img: 'assets/images/item-robust.png'
            },
            'MIX': {
                label: 'MULTIX',
                img: 'assets/images/item-multix.png'
            },
            'SUB': {
                label: 'SUBLIM',
                img: 'assets/images/item-sublim.png'
            },
            'HYD': {
                label: 'HYDRO',
                img: 'assets/images/item-hydro.png'
            }
        }
        const result = mapping[code] ? mapping[code] : {};
        return result;
    }

    getLoadDispatchTimeRange(currentDate: any) {
        const LoadDispatchDateFrom = moment(currentDate).subtract(1, 'hour').format('YYYY/MM/DD');
        const LoadDispatchDateTo = moment(currentDate).format('YYYY/MM/DD');
        const LoadDispatchTimeFrom = moment(currentDate).subtract(1, 'hour').format('HHmmss');
        const LoadDispatchTimeTo = moment(currentDate).format('HHmmss');
        return [LoadDispatchDateFrom, LoadDispatchDateTo, LoadDispatchTimeFrom, LoadDispatchTimeTo];
    }
}


