import { Order } from "@mycimencam-cron/modules/update-paid-orders/entities";
import { ReloadBalance } from "@mycimencam-cron/modules/update-paid-orders/entities/reload-balance.entity";

export type PaymentPayload = {
  order: {
    _id: string;
    appReference: string;
  };
  paymentInfo: Payment;
  amount: number;
}

export type AfrilandPaymentPayload = {
  order: {
    _id: string;
    appReference: string;
  };
  paymentInfo: Payment & { afrilandKey: string };
  amount: number;
}

export type LoadDispatchDates = {
  dateFrom: string;
  dateTo: string;
  timeFrom: string;
  timeTo: string;
}

export enum PaymentMode {
  ORANGE_MONEY = 0,
  MOBILE_MONEY = 1,
  M2U = 2,
  AFRILAND = 3,
  MY_ACCOUNT = 4,
  EXPRESS_EXCHANGE = 5,
  CREDIT = 6,
  VISA = 7,
  LOW_BALANCE = 8,
  EU = 9
}


export type PaymentInit = {
  message: string;
  data: {
    id?: number;
    createtime?: string;
    subscriberMsisdn?: string;
    amount?: number;
    payToken?: string;
    txnid?: string;
    txnmode?: string;
    inittxnmessage?: string,
    inittxnstatus?: number;
    confirmtxnstatus?: unknown,
    confirmtxnmessage?: string | unknown,
    status?: string,
    notifUrl?: string,
    description?: string,
    channelUserMsisdn?: string;
  }
}

export type Payment = {
  _id: string;
  mode: {
    id: PaymentMode;
    label: string;
  };
  clientOption?: {
    id: number;
    label: string;
  };
  data?: {
    reference: string;
    amount: number;
    bank: {
      id: number;
      label: string;
    }
  };
  order?: Partial<Order>;
  reloadBalance?: Partial<ReloadBalance>;
  tel: number;
  processingNumber?: string | number;
  transactionId?: string;
  isQ1: boolean;
  status: string;
  nbrTries?: number;
  paymentInfo?: any;
}

export type ProviderToken = {
  access_token: string;
  token_type: string;
  expires_in: number;
  start_time: number;
  scope?: string;
}

export type OrangePaymentToken = {
  message: string;
  data: {
    payToken: string;
  };
}

export type CallBackBody = {
  status?: any;
  payToken: string | any;
  message: any | string;
}

export enum TransactionStatus {
  PENDING = 200,
  SUCCESSFUL = 300,
  INITIATED = 100,
  FAILED = 99,
  EXPIRED = 400,
  CREATED = 10
}
export enum EUTransactionStatus {
  SUCCESSFUL = 200,
  PENDING = 101,
  INITIATED = 100,
  FAILED = 404,
}

export enum OperatorTransactionStatus {
  PENDING = 'PENDING',
  INITIATED = 'INITIATED',
  FAILED = 'FAILED',
  EXPIRED = 'EXPIRED',
  SUCCESSFUL = 'SUCCESSFUL',
}

export enum keyEventJDE {
  CREATE_ORDER = 'FLOW_0',
  ORDER_DETAILS = 'FLOW_1',
  DISPATCH_DETAILS = 'FLOW_2',
  GET_BALANCE = 'FLOW_3',
  LOAD_DISPATCH_DETAILS = 'FLOW_4',
  GET_REPORT = 'FLOW_5',
  CREATE_RELOAD_BALANCE = 'FLOW_7',
  CHECK_STATUS_PAYMENT_RELOAD_BALANCE = 'FLOW_8'

}

export enum AEstatus {
  C9 = 200,
  A0 = 100
}
