import { Controller } from '@nestjs/common';
import { EuCronService } from './eu-cron.service';
import { EventPattern } from '@nestjs/microservices';
import { PaymentMode } from '@mycimencam-cron/types';
import { <PERSON>noLogger } from 'nestjs-pino';

@Controller('eu-cron')
export class EuCronController {
  constructor(
    private readonly logger: PinoLogger,
    private readonly euCronService: EuCronService
    ) {}

  // @EventPattern({ cmd: `${PaymentMode.MOBILE_MONEY}_reloadBalance` })
  // async verifyMtnPaymentRequestStatus(transactionId: string) {
  //     this.logger.info(`Reicieve reload balance to verify payment status at MTN: ${JSON.stringify(transactionId)}`);
  //     return await this.euCronService.verificationEUPayment();
  // }
}
