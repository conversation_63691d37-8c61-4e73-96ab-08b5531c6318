import { Test, TestingModule } from '@nestjs/testing';
import { ClientStockController } from './client-stock.controller';
import { ClientStockService } from './client-stock.service';

describe('ClientStockController', () => {
  let controller: ClientStockController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ClientStockController],
      providers: [ClientStockService],
    }).compile();

    controller = module.get<ClientStockController>(ClientStockController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
