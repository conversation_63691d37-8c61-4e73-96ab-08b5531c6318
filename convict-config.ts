import convict from 'convict';

export const config = convict({
    env: {
        doc: 'The application environment.',
        format: ['production', 'development', 'staging'],
        default: 'development',
        env: 'NODE_ENV'
    },
    ip: {
        doc: 'The IP address to bind.',
        format: String,
        default: '127.0.0.1',
        env: 'IP_ADDRESS',
    },
    port: {
        doc: 'The port to bind.',
        format: Number,
        default: 3000,
        env: 'PORT',
        arg: 'port'
    },
    host: {
        doc: 'Application host.',
        format: String,
        default: 'localhost',
        env: 'HOST',
    },
    db: {
        host: {
            doc: 'Database host name/IP',
            format: String,
            default: 'mongodb://127.0.0.1:27017',
            env: 'DB_MONGO_HOST',
        },
        name: {
            doc: 'Database name',
            format: String,
            default: '',
            env: 'DB_MONGO_NAME',
        },
        auth: {
            user: {
                doc: 'Database user if any',
                format: String,
                default: '',
                env: 'DB_MONGO_USERNAME'
            },
            password: {
                doc: 'Database password if any',
                format: String,
                default: '',
                env: 'DB_MONGO_PASSWORD'
            }
        }
    },
    baseUrl: {
        doc: 'API base url.',
        format: String,
        default: '',
        env: 'BASE_URL',
        arg: 'base-url'
    },
    basePath: {
        doc: 'API base path.',
        format: String,
        default: '',
        env: 'BASE_PATH'
    },
    db_cimfig: {
        host: {
            doc: 'Database host name/IP',
            format: String,
            default: '127.0.0.1:27017',
            env: 'DB_CIMFIG_MONGO_HOST',
        },
        name: {
            doc: 'Database name',
            format: String,
            default: '',
            env: 'DB_CIMFIG_MONGO_NAME',
        },
        auth: {
            user: {
                doc: 'Database user if any',
                format: String,
                default: '',
                env: 'DB_CIMFIG_MONGO_USERNAME',
            },
            password: {
                doc: 'Database password if any',
                format: String,
                default: '',
                env: 'DB_CIMFIG_MONGO_PASSWORD',
            },
        },
        mongoUrl: {
            doc: 'Database name',
            format: String,
            default: '',
            env: 'DB_CIMFIG_MONGO_URL',
        },
    },
    payment: {
        mtn: {
            baseUrl: {
                doc: 'MTN base url for payment.',
                format: String,
                default: '',
                env: 'MTN_BASE_URL'
            },
            user: {
                doc: 'MTN user name',
                format: String,
                default: '',
                env: 'MTN_USERNAME'
            },
            password: {
                doc: 'MTN user password',
                format: String,
                default: '',
                env: 'MTN_PASSWORD'
            },
            subscription_Key: {
                doc: 'MTN Ocp-Apim-Subscription-Key',
                format: String,
                default: 'oracle',
                env: 'MTN_SUBSCRIPTION_KEY'
            },
            currency: {
                doc: 'MTN payment currency.',
                format: String,
                default: ''
            },
            enviroment: {
                doc: 'MTN enviroment',
                format: String,
                default: 'oracle',
                env: 'MTN_ENVIROMENT'
            },
        },
        orange: {
            baseUrl: {
                doc: 'ORANGE base url for payment.',
                format: String,
                default: '',
                env: 'ORANGE_BASEURL'
            },
            baseUri: {
                doc: 'ORANGE base uri for payment.',
                format: String,
                default: '',
                env: 'ORANGE_BASEURL'
            },
            token: {
                doc: 'ORANGE Bearer token.',
                format: String,
                default: '',
                env: 'ORANGE_TOKEN'
            },
            merchantKey: {
                doc: 'ORANGE payment merchant key.',
                format: String,
                default: '',
                env: 'ORANGE_MERCHANTKEY'
            },
            api_username: {
                doc: 'ORANGE payment api user name.',
                format: String,
                default: ''
            },
            api_password: {
                doc: 'ORANGE payment api password.',
                format: String,
                default: ''
            },
            clientId: {
                doc: 'ORANGE payment client id.',
                format: String,
                default: '',
                env: 'ORANGE_CLIENTID'
            },
            clientSecret: {
                doc: 'ORANGE payment client secret key.',
                format: String,
                default: ''
            },
            currency: {
                doc: 'ORANGE payment currency.',
                format: String,
                default: ''
            },
            pin: {
                doc: 'ORANGE payment pin.',
                format: String,
                default: '',
                env: 'ORANGE_PIN'
            },
            channelUserMsisdn: {
                doc: 'ORANGE payment channelUserMsisdn.',
                format: String,
                default: '',
                env: 'ORANGE_CHANNELUSER'
            },
            lang: {
                doc: 'ORANGE payment form language.',
                format: String,
                default: ''
            },
            reference: {
                doc: 'ORANGE payment form reference.',
                format: String,
                default: ''
            },
            notifyUrl: {
                doc: 'ORANGE notify api url.',
                format: String,
                default: ''
            },
            redirectUrl: {
                doc: 'ORANGE redirect url after successful payment.',
                format: String,
                default: ''
            },
            cancelUrl: {
                doc: 'ORANGE redirect url failed successful payment.',
                format: String,
                default: ''
            },
        },
    },
    paymentMicroService: {
        port: {
            doc: 'The payment port to bind.',
            format: Number,
            default: 3000,
            env: 'PAYMENT_PORT',
            arg: 'payment_port',
        },
        host: {
            doc: 'The payment host name or ip to bind.',
            format: String,
            default: 'localhost',
            env: 'PAYMENT_HOST',
        },
    },
    queue: {
        port: {
            doc: 'The queue port to bind.',
            format: Number,
            default: 3003,
            env: 'QUEUE_PORT',
            arg: 'queue_port'
        },
        host: {
            doc: 'The queue host name or ip to bind.',
            format: String,
            default: 'localhost',
            env: 'QUEUE_HOST',
        },
    },
    jde: {
        port: {
            doc: 'The jde port to bind.',
            format: Number,
            default: 3004,
            env: 'JDE_PORT',
            arg: 'jde_port'
        },
        host: {
            doc: 'The jde host name or ip to bind.',
            format: String,
            default: 'localhost',
            env: 'jde_HOST',
        },
    },
    loadDispatch: {
        apiUrl: {
            doc: 'LoadDispatch api url',
            format: String,
            default: 'https://jde.mycimencam.com/api/v1',
            env: 'LOADDISPATCH_API_URL'
        },
        login: {
            doc: 'LoadDispatch Auth login',
            format: String,
            default: 'loadDispatch_user_mcm',
            env: 'LOADDISPATCH_LOGIN'
        },
        password: {
            doc: 'LoadDispatch Auth password',
            format: String,
            default: 'GG4A&LK#RtzA2TAZQy&deZnv',
            env: 'LOADDISPATCH_PASSWORD'
        }
    },
    activeSnitch: {
        doc: 'Varaible to active snitch.',
        format: Boolean,
        default: true,
        env: "ACTIVE_SNITCH"
    },
    activeCronCreateOrders: {
        doc: 'Variable for active cron to send orders for creation in JDE.',
        format: Boolean,
        default: true,
        env: "ACTIVE_CRON_CREATE_ORDERS"
    },
    activeCronForBl: {
        doc: 'Variable for active cron to register Bl.',
        format: Boolean,
        default: true,
        env: "ACTIVE_CRON_BL"
    },
    activeCronCheckOrangePayment: {
        doc: 'Variable for active cron to register Bl.',
        format: Boolean,
        default: true,
        env: "ACTIVE_CRON_CHECK_ORANGE_PAYMENT"
    },
    activeCronCheckMtnPayment: {
        doc: 'Variable for active cron to register Bl.',
        format: Boolean,
        default: false,
        env: "ACTIVE_CRON_CHECK_MTN_PAYMENT"
    },
    activeCronCheckEUPayment: {
        doc: 'Variable for active cron to register Bl.',
        format: Boolean,
        default: true,
        env: "ACTIVE_CRON_CHECK_EU_PAYMENT"
    },
    activeCronSendCommercialReportNotif: {
        doc: 'Variable to send commercial report.',
        format: Boolean,
        default: true,
        env: "ACTIVE_CRON_SEND_COMMERCIAL_REPORT"
    },
    paymentRequestForReloadBalanceInJDE: {
        doc: 'Variable for active cron to register Bl.',
        format: Boolean,
        default: true,
        env: "ACTIVE_CRON_PAYMENT_REQUEST_JDE`"
    },
    requestForRetrievementJDE: {
        doc: 'Variable for active cron to register AE.',
        format: Boolean,
        default: true,
        env: "ACTIVE_CRON_REQUEST_AES_JDE"
    },
    requestForBlJDE: {
        doc: 'Variable for active cron to register Bl.',
        format: Boolean,
        default: true,
        env: "ACTIVE_CRON_REQUEST_RETRIEVEMENT_JDE"
    },
    activeCron: {
        doc: 'Variable for active cron to send orders for creation in JDE.',
        format: Boolean,
        default: true,
        env: "ACTIVE_CRON_CREATE_ORDERS"
    },
    activeCronFOrNotifyEEX: {
        doc: 'Variable for active cron to send notification to express exchange.',
        format: Boolean,
        default: false,
        env: "ACTIVE_CRON_NOTIFY_EEX"
    },
    telServiceClient: {
        doc: 'Variable for client service',
        format: String,
        default: '654900000',
        env: "CLIENT_SERVICE_NBER"
    },
    eexCronExecutionTime: {
        doc: 'Variable for executing daily report',
        format: String,
        default: '',
        env: "EEX_EXECUTION_TiME"
    },
    smsReceiverNumber: {
        doc: 'Numéro de téléphone pour l\'envoi des SMS lors de la sixieme tentative de création de la commande dans JDE',
        format: String,
        default: '655652785',
        env: 'SMS_RECEIVER_NUMBER'
    },
    systemName: {
        doc: 'Variable for system Name',
        format: String,
        default: 'MCM',
        env: "SYSTEM_NAME"
    },
    LoadDispatchQuerySystemName: {
        doc: 'Variable for system query name in LoadDispatch',
        format: String,
        default: 'mycimencamV3',
        env: "LOADISPATCH_QUERY_SYSTEM_NAME"
    },
    nbrTries: {
        doc: 'Number of tries to create order in JDE',
        format: Number,
        default: 15,
        env: "NB_TRIES"
    },
    concurrency: {
        doc: 'Number of concurrent requests to be executed',
        format: Number,
        default: 10,
        env: "CONCURRENCY"
    },

});

const env = config.get('env');
config.loadFile('./src/envs/' + env + '.json');

config.validate({ allowed: 'strict' });
