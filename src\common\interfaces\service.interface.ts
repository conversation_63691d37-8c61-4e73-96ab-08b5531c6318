import { Document, WithId } from "mongodb";

export interface ServiceInterface {

    create(data: Document, db: string): Promise<QueryResult>

    findAll(query: QueryOptions, db: string): Promise<getAllResult>

    findOne(query: QueryOptions, db: string): Promise<WithId<Document> | QueryResult>

    count(query: QueryFilter, db: string): Promise<number | QueryResult>;

    update(filter: QueryFilter, data: Document, db: string): Promise<QueryResult>;

}