export class Removal {
  itemId: string;
  removalType?: RemovalType;
  tonneQuantity: number;
  quantity: number;
  unitPrice: number;
  schedules: Schedule[]
}

export interface Schedule {
  hourTime?: string;
  removalDate: string;
  nbrTonnage: number;
  nbrTruck?: number;
  quartTime?: string;
}

export enum RemovalType {
  PerQuantity = 1,
  perDate = 2
}

//TODO: delete this interface
export interface RemovalJDE {
  label: string;
  erpItemId: number;
  erpReference: string;
  bagWeight: number;
  quantity: number;
  period: string,
  date: string;
  time: string;
}

export const ValuesQuartTime = {
  ["06H - 13H"]: "Q1",
  ["13H - 21H"]: "Q2",
  ["21H - 06H"]: "Q3",
}