import { lastValue<PERSON>rom } from 'rxjs';
import { <PERSON><PERSON> } from 'cache-manager';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'nestjs-pino';
import { ClientProxy } from '@nestjs/microservices';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { CACHE_MANAGER, Inject, Injectable } from '@nestjs/common';
import { UserCategory } from '../update-paid-orders/entities/user.entity';
import { Order, OrderStatus } from '../update-paid-orders/entities';
import { EmployeeCimencam } from '../update-paid-orders/entities/user.entity';
import { UserRepository } from '../update-paid-orders/repository/user.repository copy';
import { PaymentRepository } from './../update-paid-orders/repository/payment.repository';
import { CompanyRepository, OrderRepository, ReloadBalanceRepository } from '../update-paid-orders/repository';
import { OperatorTransactionStatus, Payment, PaymentMode, ProviderToken, TransactionStatus, keyEventJDE } from '@mycimencam-cron/types';
import { OnEvent, EventEmitter2 } from '@nestjs/event-emitter';
import { CommonService } from '@mycimencam-cron/common';
import { StoreDatabaseType } from '@mycimencam-cron/common/services/common.entity';
import { config } from 'convict-config';
import { UpdatePaidOrdersService } from '../update-paid-orders/update-paid-orders.service';


@Injectable()
export class MomoCronService {
    private isProcessing = false;

    constructor(
        private readonly logger: PinoLogger,
        private userRepository: UserRepository,
        private orderRepository: OrderRepository,
        private paymentRepo: PaymentRepository,
        private commonSrv: CommonService,
        private updatePaidOrdersService: UpdatePaidOrdersService,
        private companyRepo: CompanyRepository,
        private eventEmitter: EventEmitter2,
        private reloadBalanceRepository: ReloadBalanceRepository,
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
        @Inject('JDE') private readonly JDEService: ClientProxy,
        @Inject('QUEUE') private readonly queueClient: ClientProxy,
        @Inject('PAYMENT') private readonly paymentClient: ClientProxy,

    ) {
    }

    @Cron(CronExpression.EVERY_MINUTE, { disabled: config.get('activeCronCheckMtnPayment') })
    async startMTNVerification() {
        if (this.isProcessing) {
            this.logger.warn('MCM verificationMTNPaymentMCN cron is already running; skipping');
            return;
        }
        this.isProcessing = true;
        this.logger.info(`=|=|=| Start Cron verificationMTNPaymentMCN for ${config.get('systemName')} |=|=|=
            \n variable: ACTIVE_CRON_CHECK_MTN_PAYMENT_MCM\n`);
        try {
            await this.verificationMTNPayment();
        } finally {
            this.isProcessing = false;
            this.logger.info('MCM verificationMTNPaymentMCN cron end');
        }
    }

    /**
     * Fetch payments in pages and process each page with a small worker pool.
     */
    private async verificationMTNPayment() {
        const pageSize = config.get('nbrTries');
        const concurrency = config.get('concurrency');
        const baseQuery = {
            status: { $nin: [OperatorTransactionStatus.SUCCESSFUL, OperatorTransactionStatus.FAILED] },
            "paymentInfo.mode.id": PaymentMode.MOBILE_MONEY,
            $or: [{ nbrTries: { $lte: config.get('nbrTries') } }, { nbrTries: { $exists: 0 } }],
        };

        let page = 0;
        while (true) {
            const payments = (await this.paymentRepo.findAll({
                filter: baseQuery,
                offset: page * pageSize,
                limit: pageSize
            })) as unknown as Payment[];

            this.logger.info(`There are ${payments?.length} MTN payments`);

            if (!payments?.length) break;

            this.logger.info(`Processing page ${page + 1}, ${payments?.length} MTN payments`);
            await this.runWithConcurrency(payments, concurrency);
            page++;
        }
    }

    /**
     * Simple worker-pool: spawn `concurrency` loops that each take next payment to process.
     */
    private async runWithConcurrency(payments: Payment[], concurrency: number) {
        let idx = 0;
        const worker = async () => {
            while (idx < payments?.length) {
                const payment = payments[idx++];
                try {
                    await this.processMtnPayment(payment);
                } catch (err) {
                    this.logger.error(`Error on payment ${payment?._id}: ${JSON.stringify(err)}`);
                }
            }
        };
        await Promise.all(Array(concurrency).fill(null).map(() => worker()));
    }

    /**
     * Process a single MTN payment: update tries, call MTN, route to order or reloadBalance
     */
    private async processMtnPayment(payment: Payment) {
        const nbrTries = (payment?.nbrTries ?? 0) + 1;
        await this.paymentRepo.update({ _id: payment._id }, { "nbrTries": config.get('nbrTries') });

        this.logger.info(`MTN verify ${payment?.transactionId} for ${payment?.order?.appReference || payment?.reloadBalance?._id}`);

        const res = await lastValueFrom(
            this.paymentClient.send(
                { cmd: `${PaymentMode.MOBILE_MONEY}_payment_verification` },
                this.commonSrv.EventData(payment?.transactionId)
            )
        );

        this.logger.info(`MTN response ${payment?.transactionId}: ${JSON.stringify(res)}`);

        if ('order' in payment && 'status' in res) {
            await this.eventEmitter.emitAsync('update_order_momo', res, payment?.order._id);
        }

        if ('reloadBalance' in payment && [OperatorTransactionStatus.SUCCESSFUL, OperatorTransactionStatus.PENDING, OperatorTransactionStatus.FAILED].includes(res?.status)) {
            const reload = await this.reloadBalanceRepository.findOne({ filter: { _id: payment?.reloadBalance?._id } });
            await this.reloadBalanceRepository.update(
                { _id: payment?.reloadBalance?._id },
                { "paymentInfo": { ...reload?.paymentInfo, ...res } },
            );
        }
    }

    async verificationMTNPaymentForReloadBalance(reloadBalanceId: string) {
        const query = {
            "reloadBalance._id": reloadBalanceId,
            $or: [{ nbrTries: { $lte: config.get('nbrTries')  } }, { nbrTries: { $exists: 0 } }],
        };

        try {
            const paymentMtn = await this.paymentRepo.findOne({ filter: query }) as unknown as Payment;
            if (!paymentMtn) {
                this.logger.warn(`No payment found for reload balance ${reloadBalanceId}`);
                return;
            }

            await this.processMtnPayment(paymentMtn);
        } catch (error) {
            this.logger.error(`Error while running verificationMTNPaymentForReloadBalance: ${JSON.stringify(error)}`);
            return error;
        }
    }

    @OnEvent('update_order_momo', { async: true })
    async verifyMtnPaymentStatus(response: { status: OperatorTransactionStatus; reason: any; financialTransactionId: any; }, orderId: string) {
        try {
            const order = await this.orderRepository.findOne({ filter: { _id: orderId } }) as unknown as Order;

            if (response?.status === OperatorTransactionStatus.FAILED && response?.reason) {
                this.logger.error(`MTN payment verification response for order ${order?._id} is failed. \n${JSON.stringify(response?.reason)}\n`);
                return await this.orderRepository.update({ _id: order._id }, { status: OrderStatus.FAILD });
            }

            if (!response?.status && !response?.reason) return {
                message: `order id ${order?._id} payment process error`,
                status: 500
            }

            if (response.status === OperatorTransactionStatus.SUCCESSFUL) {
                const { payment: { mode: { id } } } = order;
                await this.commonSrv.VerifyPointEligibility(id, order, this.companyRepo);

                await this.orderRepository.update({ _id: order?._id }, {
                    status: OrderStatus.PAID,
                    'payment.transactionID': response.financialTransactionId,
                    'dates.paid': (new Date()).getTime()
                });
                await this.updatePaidOrdersService.integrateStockThresholdNotifications(order);


                const user = await this.userRepository.findOne({ filter: { _id: order?.user?._id } }) as unknown as EmployeeCimencam;

                if (user.category === UserCategory.Particular) {
                    const pointValidated = user['points']?.validated + this.computeUserPoint(order);
                    await this.userRepository.update({ _id: user._id }, { 'points.validated': pointValidated });

                    // await ordersCollection.updateOrder(orderId, { points: computeUserPoint(order) });
                }

                if (user?.category === UserCategory.EmployeeCimencam && 'tonnage' in user) {
                    const capacityTonnage = user.tonnage.capacity - this.computeOrderTonnage(order);
                    const capacityTonnageYear = user?.tonnage?.capacityPerYear - this.computeOrderTonnage(order);
                    await this.userRepository.update({ _id: user._id }, { capacityTonnage, capacityTonnageYear });
                    this.logger.info(`Tonnage of ${user?.email} has been updated to capacityTonnage: ${capacityTonnage} and capacityTonnageYear ${capacityTonnageYear}.`);
                }

                this.JDEService.emit({ cmd: keyEventJDE.CREATE_ORDER }, this.commonSrv.EventData({ appReference: order.appReference }));

                this.queueClient.emit('payment_received', this.commonSrv.EventData(order));
                this.queueClient.emit('commercial_validation', this.commonSrv.EventData(order));

                this.logger.info(`email notification sent to client and cimencam`);

                return {
                    message: `order id ${order._id} payment success`,
                    status: 200
                }

            }
        } catch (error) {
            this.queueClient.emit('send_error', this.commonSrv.EventData({
                message: error,
                microService: 'CRON',
                methodPath: 'verifyMtnPaymentStatus'
            }));
            this.logger.error(`Error during procesing of response from MTN ${error}`)
        }
    }

    private computeUserPoint(order: Order) {
        let points = 0;
        order.cart.items.forEach(item => {
            if (item?.product?.label === 'ROBUST') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 2; }
            if (item?.product?.label === 'MULTIX') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 2; }
            if (item?.product?.label === 'SUBLIM') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 3; }
            if (item?.product?.label === 'HYDRO 50kg') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 3; }
            if (item?.product?.label === 'HYDRO 25kg') { points = points + (item.quantity / order?.cart?.packaging?.unit?.ratioToTone) * 3; }
        });
        return points;
    }

    private computeOrderTonnage(order: Order): number {
        let tonnage = 0;
        order.cart.items.forEach(item => {
            if (item.quantity) { tonnage += (item?.quantity / order?.cart?.packaging?.unit?.ratioToTone); }
        });
        return tonnage;
    }

}
