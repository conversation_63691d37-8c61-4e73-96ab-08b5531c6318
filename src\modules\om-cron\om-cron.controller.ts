import { Controller } from "@nestjs/common";
import { EventPattern } from "@nestjs/microservices";
import { OmCronService } from "./om-cron.service";
import { PinoLogger } from "nestjs-pino";
import { PaymentMode } from "@mycimencam-cron/types";

@Controller()
export class OmCronController {

    constructor(
        private readonly logger: PinoLogger,
        private readonly omCronService: OmCronService) { }

}