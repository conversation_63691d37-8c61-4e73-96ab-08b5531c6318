{"name": "mycimencam-cron", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build && npm run copy-files", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:staging": "cross-env NODE_ENV=staging node src/main.js", "start:prod": "cross-env NODE_ENV=production node src/main.js", "copy-files": "copyfiles -u 1 src/**/*.json dist/src/", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^8.0.0", "@nestjs/core": "^8.0.0", "@nestjs/event-emitter": "^2.0.3", "@nestjs/mapped-types": "^1.2.1", "@nestjs/microservices": "^8.0.0", "@nestjs/platform-express": "^8.0.0", "@nestjs/schedule": "^4.0.1", "@types/http-server": "^0.12.1", "@types/request-promise": "^4.1.48", "base-64": "^1.0.0", "cache-manager": "^4.1.0", "convict": "^6.2.3", "cross-env": "^7.0.3", "event-emitter": "^0.3.5", "eventemitter2": "^6.4.9", "http": "^0.0.1-security", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "mongodb": "^4.9.0", "nestjs-pino": "^3.1.2", "pino-http": "^8.3.3", "pino-pretty": "^10.0.0", "reflect-metadata": "^0.1.13", "request": "^2.88.2", "request-promise": "^4.2.6", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^8.0.0", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^8.0.0", "@types/cache-manager": "^4.0.1", "@types/convict": "^6.1.1", "@types/cron": "^2.0.0", "@types/express": "^4.17.13", "@types/jest": "27.4.1", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "copyfiles": "^2.4.1", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^27.2.5", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "^27.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^3.10.1", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}