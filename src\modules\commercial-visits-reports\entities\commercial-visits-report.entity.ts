import { Company } from "@mycimencam-cron/modules/update-paid-orders/entities/company.entity";
import { CompanyEmployee } from "@mycimencam-cron/modules/update-paid-orders/entities/user.entity";

export class SalesVisit {
    title: string;
    companies: Partial<Company>[];
    description: string;
    periodicity: string;
    regions: string[];
    endDate: number;
    commercial: CompanyEmployee;
    startDate: number;
    enable: boolean;
    created_at: number;
}
