import { Test, TestingModule } from '@nestjs/testing';
import { EexCronController } from './eex-cron.controller';
import { EexCronService } from './eex-cron.service';

describe('EexCronController', () => {
  let controller: EexCronController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EexCronController],
      providers: [EexCronService],
    }).compile();

    controller = module.get<EexCronController>(EexCronController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
