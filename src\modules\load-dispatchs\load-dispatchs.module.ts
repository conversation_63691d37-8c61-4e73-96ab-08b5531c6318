import { Module } from '@nestjs/common';
import { LoadDispatchsService } from './load-dispatchs.service';
import { Authorization_RemovalRepository } from './repository';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { config } from 'convict-config';
import { CompanyRepository, OrderRepository, SchedulesRepository } from '../update-paid-orders/repository';
import { UserRepository } from '../update-paid-orders/repository/user.repository copy';
import { CommonService } from '@mycimencam-cron/common';

@Module({
  imports: [
    ClientsModule.register([
      { name: "JDE", transport: Transport.TCP, options: { host: config.get('jde.host'), port: config.get('jde.port') } },
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
    ]),
  ],
  providers: [LoadDispatchsService, Authorization_RemovalRepository, OrderRepository, UserRepository, CompanyRepository, SchedulesRepository, CommonService]
})
export class LoadDispatchsModule { }
