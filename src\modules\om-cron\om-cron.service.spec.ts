import { Test, TestingModule } from '@nestjs/testing';
import { OmCronService } from './om-cron.service';

describe('OmCronService', () => {
  let service: OmCronService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [OmCronService],
    }).compile();

    service = module.get<OmCronService>(OmCronService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
