import { Order } from '@mycimencam-cron/modules/update-paid-orders/entities';
import moment from 'moment';


export class AutomaticValidatedOrderEvent {
  customer_name: string;
  order_ref: string;
  order_paid_date: string;
  order_created_date: string;
  total: number;
  payment_ref: string;
  payment_mode: string;
  payment_mode_option: string;
  payment_mode_amount: number;
  payment_mode_bank: string;
  order_products: { item: string; quantity: number }[];

  constructor(order: Order) {
    this.customer_name = order?.company ? order?.company?.name : order?.user?.firstName + ' ' + order?.user?.lastName 
    this.order_ref = order?.appReference || order?.customerReference ;
    this.order_paid_date = moment(order?.dates?.paid).format('DD MMM YYYY');
    this.order_created_date = moment(order?.created_at).format('DD MMM YYYY');
    this.order_products = this.formatItems(order?.cart?.items, order?.cart?.packaging?.unit?.ratioToTone);
    this.total = order?.cart?.amount?.TTC;
    this.payment_ref = order?.payment?.transactionId;
    this.payment_mode = order.payment.mode.label;
    this.payment_mode_option = order?.payment?.clientOption?.label;
    this.payment_mode_amount = order?.payment?.data?.amount;
    this.payment_mode_bank = order?.payment?.data?.bank?.label;
  
  }

  private formatItems(cart: any[], unit: number) {
    return cart?.map((item) => {
      return {
        item: item?.product?.label,
        quantity: (item?.quantity * unit) / 1000,
      };
    });
  }

}