import { Address } from "cluster";
import { CompanyCategory } from "./company.entity";
import { Store } from "./store.entity";
import { UserCategory } from "./user.entity";

export class GlobalShipping {
    _id?: string;
    startRef: Partial<Store>;
    endRef: string;
    label: string;
    amount: number;
    enable?: boolean;
    create_at?: number;

    static constructPartialShipping(currentShipping: any) {
        const newShipping: CompanyShipping = {
            _id: currentShipping?._id,
            startRef: currentShipping?.startRef,
            erpShipToId: currentShipping?.erpShipToId,
            erpShipToDesc: currentShipping?.erpShipToDesc,
            amount: currentShipping?.amount,
            companyId: currentShipping?.company?._id.toString(),
            category: currentShipping?.category,
            endRef: currentShipping?.endRef,
            label: currentShipping?.label,
            enable: currentShipping?.enable
        }
        return newShipping;
    }
}

export class CompanyShipping extends GlobalShipping {
    companyId: string;
    category: CompanyCategory;
    erpShipToId: number;
    erpShipToDesc: string;
}

export class ParticularShipping extends GlobalShipping {
    category: UserCategory;
    address: Address;
}

export declare type Shipping = GlobalShipping | CompanyShipping | ParticularShipping;