import { config } from 'convict-config';
import { Module } from '@nestjs/common';
import { JdeCronService } from './jde-cron.service';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { CompanyRepository, ReloadBalanceRepository, RetrievementRepository } from '../update-paid-orders/repository';
import { UpdatePaidOrdersModule } from '../update-paid-orders/update-paid-orders.module';

@Module({
  imports: [
    ClientsModule.register([
      { name: "JDE", transport: Transport.TCP, options: { host: config.get('jde.host'), port: config.get('jde.port') } },
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
    ]),
    UpdatePaidOrdersModule,
  ],
  providers: [JdeCronService, ReloadBalanceRepository, CompanyRepository, RetrievementRepository]
})
export class JdeCronModule { }
