import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ScheduleModule } from '@nestjs/schedule';
import { UpdatePaidOrdersModule } from './modules/update-paid-orders/update-paid-orders.module';
import { MomoCronModule } from './modules/momo-cron/momo-cron.module';
import { JdeCronModule } from './modules/jde-cron/jde-cron.module';
import { LoadDispatchsModule } from './modules/load-dispatchs/load-dispatchs.module';
import { LoggerModule } from 'nestjs-pino';
import { OmCronModule } from './modules/om-cron/om-cron.module';
import { EuCronModule } from './modules/eu-cron/eu-cron.module';
import { CommonModule } from './common/common.module';
import { EexCronModule } from './modules/eex-cron/eex-cron.module';
import { CommercialVisitsReportsModule } from './modules/commercial-visits-reports/commercial-visits-reports.module';

@Module({
  imports: [
    JdeCronModule,
    MomoCronModule,
    EuCronModule,
    LoadDispatchsModule,
    UpdatePaidOrdersModule,
    ScheduleModule.forRoot(),
    LoggerModule.forRoot({
      pinoHttp: {
        transport: {
          target: 'pino-pretty',
          options: {
            singleLine: true,
            colorize: true,
            levelFirst: false,
            translateTime: "yyyy-mm-dd'T'HH:MM:ss.l'Z'",
            messageFormat: '{req.headers.x-correlation-id} [{context}] {msg}',
            ignore: 'pid,hostname,context,req,res,responseTime',
            errorLikeObjectKeys: ['err', 'error'],
          },
        },
      },
    }),
    OmCronModule,
    CommonModule,
    EexCronModule,
    CommercialVisitsReportsModule
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule { }
