import { Document, InsertOneResult, UpdateResult, WithId } from "mongodb";

export interface RepositoryInterface {

    create(data: Document, db: string): Promise<InsertOneResult<Document>>;

    findAll(query: QueryOptions, db: string): Promise<Document[]>;

    findOne(query: QueryOptions, db: string): Promise<WithId<Document>>;

    count(query: QueryFilter, db: string): Promise<number>;

    update(filter: QueryFilter, data: Document, db: string): Promise<UpdateResult>;
}