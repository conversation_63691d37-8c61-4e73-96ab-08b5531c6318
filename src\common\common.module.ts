import { Modu<PERSON> } from "@nestjs/common";
import { config } from "convict-config";
import { CommonService } from "./services";
import { ClientsModule, Transport } from "@nestjs/microservices";

@Module({
    imports: [
        ClientsModule.register([
            { name: "J<PERSON>", transport: Transport.TCP, options: { host: config.get('jde.host'), port: config.get('jde.port') } },
          ]),
    ],
	providers: [CommonService],
    exports: [CommonService],
})
export class CommonModule {}
