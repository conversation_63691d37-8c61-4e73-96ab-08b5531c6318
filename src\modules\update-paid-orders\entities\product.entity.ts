export class Product {
    _id?: string;
    label: string;
    image: string;
    normLabel: string;
    erpRef: string;
    description: string;
    specifications?: string[] | Content[];
    advantages?: string[];
    cautions?: string[];
    cimencamAdvantages?: string[];
    applicationFields?: string[] | Content[];
    benefits?: Content[];
    enable?: boolean;
    create_at?: number;
    fidelityPoint?: number;
  }
  
  export interface Content {
    label: string;
    values: string[];
  }
  