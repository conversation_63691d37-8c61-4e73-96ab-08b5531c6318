import { Test, TestingModule } from '@nestjs/testing';
import { JdeCronService } from './jde-cron.service';

describe('JdeCronService', () => {
  let service: JdeCronService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [JdeCronService],
    }).compile();

    service = module.get<JdeCronService>(JdeCronService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
