import { Controller } from "@nestjs/common";
import { MomoCronService } from "./momo-cron.service";
import { PinoLogger } from "nestjs-pino";
import { PaymentMode } from "@mycimencam-cron/types";
import { EventPattern } from "@nestjs/microservices";

@Controller()
export class MomoCronController {

    constructor(
        private readonly logger: PinoLogger,
        private readonly momoCronService: MomoCronService) { }

}
