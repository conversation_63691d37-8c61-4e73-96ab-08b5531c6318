import { Transport, TcpOptions } from '@nestjs/microservices';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { config } from 'convict-config';

async function bootstrap() {
  const app = await NestFactory.createMicroservice<TcpOptions>(AppModule, {
    transport: Transport.TCP,
    options: {
      port: config.get('port'),
      host: config.get('host'),
    },
  });
  await app.listen();
  console.log(`Cron microservice started. Listening on port: ${config.get('port')} in "${config.get('env')}" mode`);
}
bootstrap();
