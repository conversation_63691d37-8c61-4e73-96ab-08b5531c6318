import moment from 'moment';
import { PinoLogger } from 'nestjs-pino';
import { Inject, Injectable } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Cron, CronExpression } from '@nestjs/schedule';
import { SalesVisit } from './entities/commercial-visits-report.entity';
import { SalesFeedback } from '../update-paid-orders/entities/feedback.entity';
import { StoreDatabaseType } from '@mycimencam-cron/common/services/common.entity';
import { SalesFeedbackRepository, SalesVisitRepository } from '../update-paid-orders/repository';
import { CommonService } from '@mycimencam-cron/common';
import { config } from 'convict-config';

@Injectable()
export class CommercialVisitsReportsService {

  isRunning: boolean;
  running: boolean;

  constructor(
    private commonSrv: CommonService,
    private readonly logger: PinoLogger,
    private readonly salesVisitsRepository: SalesVisitRepository,
    private readonly salesFeedbacksRepository: SalesFeedbackRepository,
    @Inject('QUEUE') private readonly queueClient: ClientProxy,

  ) { }

  @Cron(CronExpression.EVERY_DAY_AT_6PM, { disabled: config.get('activeCronSendCommercialReportNotif') })
  async verificationMTNPaymentMCM() {
    this.logger.info(`=|=|=| Start Cron verificationMTNPaymentMCM for ${config.get('systemName')} |=|=|=
             \n variable: ACTIVE_CRON_CHECK_MTN_PAYMENT_MCM\n`);

    if (this.running)
      return this.logger.warn(`Cron paymentMTNVerification is already have process, skip this execution`);

    this.running = true;
    await this.generateReportsForSalesVisits();
    this.running = false;
  }

  async generateReportsForSalesVisits() {
    const startOfDay = moment().startOf('day').valueOf(); // Start of the day (00:00:00)
    const endOfDay = moment().endOf('day').valueOf();

    const query = {
      created_at: {
        $gte: startOfDay, // Greater than or equal to the start of the day
        $lte: endOfDay    // Less than or equal to the end of the day
      }
    };

    try {
      const salesVisits = await this.salesVisitsRepository.findAll({ filter: query }) as unknown as SalesVisit[];
      const salesFeedbacks = await this.salesFeedbacksRepository.findAll({ filter: query }) as unknown as SalesFeedback[];

      // Create a map to store commercial information, visits, and feedbacks
      const commercialReportMap = new Map();

      // Process sales visits
      for (const saleVisit of salesVisits) {
        const commercialId = saleVisit.commercial?._id?.toString();

        if (!commercialReportMap.has(commercialId)) {
          commercialReportMap.set(commercialId, {
            commercial: saleVisit?.commercial, // Assuming commercial info is embedded in saleVisit
            visits: 0,
            feedbacks: 0,
            saleVisitsInfos: [], // Array to store detailed visit information
            feedbackInfos: []    // Array to store detailed feedback information
          });
        }

        commercialReportMap.get(commercialId).visits += 1;
        commercialReportMap.get(commercialId).saleVisitsInfos?.push(saleVisit); // Add visit details
      }

      // Process sales feedbacks
      for (const feedback of salesFeedbacks) {
        const commercialId = feedback.commercial?._id?.toString();

        if (commercialReportMap.has(commercialId)) {
          commercialReportMap.get(commercialId).feedbacks += 1;
          commercialReportMap.get(commercialId).feedbackInfos?.push(feedback); // Add feedback details
        }
      }

      // Convert the map to an array for easy binding to the template
      const report = Array.from(commercialReportMap.values());

      const cleanReport = this.cleanReportData(report);

      for (const currentReport of cleanReport) {
        if (currentReport?.feedbackInfos?.length === 0 || currentReport?.saleVisitsInfos?.length === 0) {
          this.logger.warn(
            `Skipping emission for commercial ${currentReport?.commercial?.lastName} due to empty feedbackInfos or saleVisitsInfos.`
          );
          continue; // Skip emitting the event for this commercial
        }
        this.queueClient.emit('commercial_report', this.commonSrv.EventData(currentReport)
        );
      }

      // Log or return the report
      this.logger.info(`Report for the day: ${JSON.stringify(cleanReport)}`);
      return report;

    } catch (error) {
      this.logger.error(`Error while running cron of verificationMTNPayment: ${JSON.stringify(error)}`);
      return error;
    } finally {
      this.isRunning = false;
    }
  }

  cleanReportData(reportArray) {
    // Extract necessary data from the report array
    const cleanedReport = reportArray?.map(report => {
      return {
        commercial_name: report.commercial.lastName + ' ' + report.commercial.firstName,
        commercial_email: report.commercial.email,
        commercial_tel: report.commercial.tel,
        commercial_address: report.commercial.address,
        nber_of_visits: report.visits,
        nber_of_feedbacks: report.feedbacks,
        created_at: moment().format('YYYY-MM-DD HH:mm:ss'),
        saleVisitsInfos: report.saleVisitsInfos?.map(visit => ({
          title: visit.title,
          description: visit.description,
          companies: visit.companies.map(company => company.name),
          regions: visit.regions
        })),
        feedbackInfos: report.feedbackInfos?.map(feedback => ({
          objectVisit: feedback.data.objectVisit,
          interlocutor: feedback.data.interlocutor,
          pdv: feedback.data.pdv,
          location: feedback.data.location,
          socialReason: feedback.data.socialReason.name,
          descriptions: {
            title: feedback.data.descriptions.title,
            descriptive: feedback.data.descriptions.descriptive
          },
        }))
      };
    });

    return cleanedReport;
  }

}
