import { config } from 'convict-config';
import { OmCronService } from './om-cron.service';
import { CacheModule, Module } from '@nestjs/common';
import { OmCronController } from './om-cron.controller';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { CommonModule } from '@mycimencam-cron/common/common.module';
import { UpdatePaidOrdersModule } from '../update-paid-orders/update-paid-orders.module';

@Module({
  controllers: [OmCronController],
  imports: [
    CacheModule.register(),
    ClientsModule.register([
      { name: "JDE", transport: Transport.TCP, options: { host: config.get('jde.host'), port: config.get('jde.port') } },
      { name: "QUEUE", transport: Transport.TCP, options: { host: config.get('queue.host'), port: config.get('queue.port') } },
      { name: "PAYMENT", transport: Transport.TCP, options: { host: config.get('paymentMicroService.host'), port: config.get('paymentMicroService.port') } },
    ]),
    EventEmitterModule.forRoot({
      wildcard: true
    }),
    CommonModule,
    UpdatePaidOrdersModule
  ],
  providers: [OmCronService]
})
export class OmCronModule { }
